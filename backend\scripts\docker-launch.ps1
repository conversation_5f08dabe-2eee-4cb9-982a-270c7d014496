# TheAIgency Backend - Docker Launch Script
# Phase 4 Completion: Production Docker Deployment

param(
    [switch]$Build,
    [switch]$Test,
    [switch]$Production,
    [switch]$Stop,
    [switch]$Logs,
    [switch]$Health,
    [switch]$Clean,
    [switch]$Help
)

# Error handling
$ErrorActionPreference = "Stop"

# Colors for output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Log($message) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-ColorOutput Blue "[$timestamp] $message"
}

function Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

function Show-Help {
    Write-Host ""
    Write-ColorOutput Cyan "TheAIgency Backend - Docker Launch Script"
    Write-Host "=========================================="
    Write-Host ""
    Write-Host "Usage: .\scripts\docker-launch.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Build       Build Docker images"
    Write-Host "  -Test        Run test environment"
    Write-Host "  -Production  Launch production environment"
    Write-Host "  -Stop        Stop all containers"
    Write-Host "  -Logs        Show container logs"
    Write-Host "  -Health      Check service health"
    Write-Host "  -Clean       Clean up containers and volumes"
    Write-Host "  -Help        Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\scripts\docker-launch.ps1 -Build -Production"
    Write-Host "  .\scripts\docker-launch.ps1 -Test"
    Write-Host "  .\scripts\docker-launch.ps1 -Health"
    Write-Host "  .\scripts\docker-launch.ps1 -Stop -Clean"
    Write-Host ""
}

if ($Help) {
    Show-Help
    exit 0
}

# Check Docker availability
try {
    docker --version | Out-Null
    Log "Docker is available"
} catch {
    Error "Docker is not available. Please install Docker Desktop."
    exit 1
}

# Check Docker Compose availability
try {
    docker-compose --version | Out-Null
    Log "Docker Compose is available"
} catch {
    Error "Docker Compose is not available. Please install Docker Compose."
    exit 1
}

# Build Docker images
if ($Build) {
    Log "Building Docker images..."
    Log "========================"
    
    try {
        Log "Building production image..."
        docker build -t theaigency-backend:latest .
        Success "Production image built successfully"
        
        Log "Building test image..."
        docker build -f Dockerfile.test -t theaigency-backend:test .
        Success "Test image built successfully"
        
        # Show image sizes
        Log "Docker images:"
        docker images | Select-String "theaigency-backend"
        
    } catch {
        Error "Failed to build Docker images: $($_.Exception.Message)"
        exit 1
    }
}

# Run test environment
if ($Test) {
    Log "Launching test environment..."
    Log "============================"
    
    try {
        Log "Starting test containers..."
        docker-compose -f docker-compose.test.yml up --abort-on-container-exit
        
        Log "Test environment completed"
        
        # Cleanup test environment
        Log "Cleaning up test containers..."
        docker-compose -f docker-compose.test.yml down -v
        Success "Test environment cleaned up"
        
    } catch {
        Error "Test environment failed: $($_.Exception.Message)"
        # Cleanup on failure
        docker-compose -f docker-compose.test.yml down -v
        exit 1
    }
}

# Launch production environment
if ($Production) {
    Log "Launching production environment..."
    Log "=================================="
    
    # Check if .env file exists
    if (-not (Test-Path ".env")) {
        Warning ".env file not found. Creating from template..."
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Warning "Please configure .env file with your production settings"
        } else {
            Error ".env.example not found. Please create .env file manually."
            exit 1
        }
    }
    
    try {
        Log "Starting production containers..."
        docker-compose up -d
        
        # Wait for services to start
        Log "Waiting for services to start..."
        Start-Sleep -Seconds 10
        
        # Check health
        Log "Checking service health..."
        $healthCheck = Test-ServiceHealth
        
        if ($healthCheck) {
            Success "🎉 Production environment launched successfully!"
            Success "✅ All services are healthy and running"
            Log ""
            Log "Service URLs:"
            Log "  Backend API: http://localhost:3000"
            Log "  Health Check: http://localhost:3000/health"
            Log "  Database: localhost:5432"
            Log "  Redis: localhost:6379"
            Log "  Qdrant: http://localhost:6333"
            Log ""
            Log "To view logs: .\scripts\docker-launch.ps1 -Logs"
            Log "To stop: .\scripts\docker-launch.ps1 -Stop"
        } else {
            Error "Some services are not healthy. Check logs for details."
            docker-compose logs
        }
        
    } catch {
        Error "Failed to launch production environment: $($_.Exception.Message)"
        docker-compose logs
        exit 1
    }
}

# Stop containers
if ($Stop) {
    Log "Stopping containers..."
    Log "====================="
    
    try {
        docker-compose down
        Success "Containers stopped successfully"
    } catch {
        Error "Failed to stop containers: $($_.Exception.Message)"
    }
}

# Show logs
if ($Logs) {
    Log "Showing container logs..."
    Log "========================"
    
    try {
        docker-compose logs -f
    } catch {
        Error "Failed to show logs: $($_.Exception.Message)"
    }
}

# Check health
if ($Health) {
    Log "Checking service health..."
    Log "========================="
    
    $healthy = Test-ServiceHealth
    
    if ($healthy) {
        Success "All services are healthy"
    } else {
        Error "Some services are unhealthy"
        exit 1
    }
}

# Clean up
if ($Clean) {
    Log "Cleaning up containers and volumes..."
    Log "===================================="
    
    try {
        # Stop and remove containers
        docker-compose down -v
        
        # Remove unused images
        docker image prune -f
        
        # Remove unused volumes
        docker volume prune -f
        
        Success "Cleanup completed"
    } catch {
        Error "Failed to clean up: $($_.Exception.Message)"
    }
}

# Function to test service health
function Test-ServiceHealth {
    $allHealthy = $true
    
    # Test backend health
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3000/health" -TimeoutSec 10
        if ($response.status -eq "ok") {
            Success "Backend API: Healthy"
        } else {
            Error "Backend API: Unhealthy"
            $allHealthy = $false
        }
    } catch {
        Error "Backend API: Not responding"
        $allHealthy = $false
    }
    
    # Test database connection
    try {
        $dbTest = docker-compose exec -T db pg_isready -U postgres
        if ($LASTEXITCODE -eq 0) {
            Success "Database: Healthy"
        } else {
            Error "Database: Unhealthy"
            $allHealthy = $false
        }
    } catch {
        Error "Database: Not responding"
        $allHealthy = $false
    }
    
    # Test Redis connection
    try {
        $redisTest = docker-compose exec -T redis redis-cli ping
        if ($redisTest -eq "PONG") {
            Success "Redis: Healthy"
        } else {
            Error "Redis: Unhealthy"
            $allHealthy = $false
        }
    } catch {
        Error "Redis: Not responding"
        $allHealthy = $false
    }
    
    # Test Qdrant connection
    try {
        $qdrantResponse = Invoke-RestMethod -Uri "http://localhost:6333/health" -TimeoutSec 10
        Success "Qdrant: Healthy"
    } catch {
        Error "Qdrant: Not responding"
        $allHealthy = $false
    }
    
    return $allHealthy
}

# Default action if no parameters provided
if (-not ($Build -or $Test -or $Production -or $Stop -or $Logs -or $Health -or $Clean)) {
    Log "TheAIgency Backend - Docker Launch Script"
    Log "========================================"
    Log ""
    Log "Phase 4 Implementation Complete ✅"
    Log "Production Ready for Docker Launch 🚀"
    Log ""
    Log "Use -Help for available options"
    Log ""
    Log "Quick start:"
    Log "  1. Build images: .\scripts\docker-launch.ps1 -Build"
    Log "  2. Run tests: .\scripts\docker-launch.ps1 -Test"
    Log "  3. Launch production: .\scripts\docker-launch.ps1 -Production"
    Log ""
}
