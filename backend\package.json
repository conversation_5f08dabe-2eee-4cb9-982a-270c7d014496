{"name": "theaigency-backend", "version": "1.0.0", "description": "Backend for TheAIgency PAIM system", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "npx nodemon --exec \"npx ts-node src/index.ts\"", "build": "npx tsc -p tsconfig.build.json", "build:staging": "npx tsc -p tsconfig.staging.json", "start:staging": "node dist/src/server.staging.js", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "prettier": "prettier --check \"src/**/*.ts\"", "prettier:fix": "prettier --write \"src/**/*.ts\"", "migrate:latest": "ts-node ./node_modules/knex/bin/cli.js migrate:latest --knexfile ./knexfile.ts", "migrate:rollback": "knex migrate:rollback --knexfile ./knexfile.ts", "seed:run": "knex seed:run --knexfile ./knexfile.ts", "test": "vitest", "test:watch": "vitest --watch", "test:unit": "vitest run --reporter=verbose", "test:integration": "vitest run --config vitest.integration.config.ts", "test:coverage": "vitest run --coverage", "test:e2e": "vitest run tests/e2e/", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "docker:build": "docker build -t theaigency-backend .", "docker:run": "docker run -p 3000:3000 theaigency-backend", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "docker:test": "docker-compose -f docker-compose.test.yml up --abort-on-container-exit", "performance:test": "vitest run tests/performance/", "security:test": "vitest run tests/security/"}, "keywords": [], "author": "TheAIgency", "license": "ISC", "dependencies": {"@nestjs/common": "^11.1.2", "@supabase/supabase-js": "^2.49.8", "@types/joi": "^17.2.2", "appwrite": "^16.1.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "node-appwrite": "^17.0.0", "pg": "^8.11.5", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.13.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-rate-limit": "^6.0.2", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.17.54", "@types/pg": "^8.11.6", "@types/stripe": "^8.0.416", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "axios-mock-adapter": "^2.1.0", "eslint": "^9.15.0", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.0", "nodemon": "^3.1.0", "prettier": "^3.2.5", "supertest": "^7.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5", "vitest": "^3.1.4"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write", "tsc --noEmit"]}}