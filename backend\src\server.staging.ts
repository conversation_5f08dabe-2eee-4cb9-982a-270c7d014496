import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import logger from './config/logger';
import db from './database/db';
import { errorHandler } from './middleware/error-handler';
import { MonitoringService } from './monitoring/monitoring.service'; // Import MonitoringService
import { WebSocketService } from './websocket/websocket.service'; // Import WebSocketService
import { CollaborationEvents } from './websocket/events/collaboration.events'; // Import CollaborationEvents
import { NotificationService } from './notifications/notification.service'; // Import NotificationService
import { NotificationRepository } from './notifications/notification.repository'; // Import NotificationRepository
import { CollaborationSessionService } from './collaboration/collaboration-session.service'; // Import CollaborationSessionService

import { PaimController } from './paim/paim.controller'; // Import PaimController
import { PowerOpsController } from './powerops/powerops.controller'; // Import PowerOps controller
import { WorkflowCollaborationController } from './workflow-collaboration/workflow-collaboration.controller'; // Import Workflow Collaboration controller
import { WorkflowCollaborationService } from './workflow-collaboration/workflow-collaboration.service'; // Import Workflow Collaboration Service
import { WorkflowCollaborationRepository } from './workflow-collaboration/workflow-collaboration.repository'; // Import Workflow Collaboration Repository
import { MonitoringRepository } from './monitoring/monitoring.repository'; // Import MonitoringRepository
import { AuditTrailService } from './audit/audit.service'; // Import AuditTrailService
import { PaimService } from './paim/paim.service'; // Import PaimService
import { PaimRepository } from './paim/paim.repository'; // Import PaimRepository
import { PowerOpsService } from './powerops/powerops.service'; // Import PowerOpsService
import { CulturalSensitivityService } from './cultural-sensitivity/cultural-sensitivity.service'; // Import CulturalSensitivityService

// Load environment variables
dotenv.config();

const app: Application = express();
const PORT = process.env.PORT || 3001;

// Security Middleware: Helmet for security headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://*.supabase.co"], // Adjust as needed for Supabase
    },
  },
  crossOriginEmbedderPolicy: false, // Allow embedding resources from other origins
}));

// CORS Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// Rate Limiting Middleware
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again after 15 minutes',
  handler: (req, res, next) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      message: 'Too many requests, please try again later.',
    });
  },
});
app.use('/api/', apiLimiter); // Apply to all API routes

// Body Parser Middleware & Request Metric Increment
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use((req: Request, res: Response, next: NextFunction) => {
  monitoringService.incrementMetric('api_requests_total');
  next();
});

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'theaigency-backend'
  });
});

// Database health check
app.get('/health/db', async (req: Request, res: Response) => {
  try {
    monitoringService.incrementMetric('db_queries_total'); // Increment DB query metric
    await db.raw('SELECT 1');
    res.status(200).json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    monitoringService.incrementMetric('db_query_errors_total'); // Increment DB query error metric
    logger.error('Database health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      database: 'disconnected',
      timestamp: new Date().toISOString()
    });
  }
});

// Metrics endpoint
app.get('/health/metrics', (req: Request, res: Response) => {
  res.status(200).json(monitoringService.getMetrics());
});

// Basic API info endpoint
app.get('/api', (req: Request, res: Response) => {
  res.json({
    name: 'TheAIgency Backend API',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Initialize WebSocket Service (needs to be done after HTTP server is created)
let wsService: WebSocketService;
// Initialize Collaboration Events
let collaborationEvents: CollaborationEvents;
// Initialize Notification Service
let notificationService: NotificationService;
// Initialize Collaboration Session Service
let collaborationSessionService: CollaborationSessionService;
// Initialize Monitoring Service
let monitoringService: MonitoringService;

// Initialize Notification Repository
const notificationRepository = new NotificationRepository();

// Initialize Workflow Collaboration dependencies
const workflowCollaborationRepository = new WorkflowCollaborationRepository();
// workflowCollaborationService will be initialized after wsService is available
let workflowCollaborationService: WorkflowCollaborationService;
let workflowCollaborationController: WorkflowCollaborationController;

// PowerOps Controller will be initialized inside server.listen
let powerOpsController: PowerOpsController;
// PAIM Controller will be initialized inside server.listen
let paimController: PaimController;

// API Routes
// All API endpoints should follow URI versioning (e.g., /api/v1/users) as per API Design Standards.
// TODO: Integrate OpenAPI documentation generation (e.g., using swagger-jsdoc or express-oas-generator)

// Remove the original registration of paim-instances routes as it's moved inside server.listen

// Remove the original registration of powerops routes as it's moved inside server.listen

// Remove the original registration of workflow-collaboration routes as it's moved inside server.listen

// 404 handler
app.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Centralized error handler (must be the last middleware)
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
  monitoringService.incrementMetric('api_errors_total'); // Increment API error metric
  errorHandler(err, req, res, next);
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`🚀 Server running on port ${PORT}`);
  logger.info(`📊 Health check: http://localhost:${PORT}/health`);
  logger.info(`🗄️  Database health: http://localhost:${PORT}/health/db`);

  // Initialize WebSocket Service
  wsService = new WebSocketService(server);
  logger.info('WebSocket server initialized and attached to HTTP server.');

  // Initialize Collaboration Events with the WebSocket service
  collaborationEvents = new CollaborationEvents(wsService);

  // Initialize Notification Service with the WebSocket service and repository
  notificationService = new NotificationService(wsService, notificationRepository);

  // Initialize Collaboration Session Service
  collaborationSessionService = new CollaborationSessionService(wsService, collaborationEvents);
  logger.info('Collaboration Session Service initialized.');

  // Initialize Monitoring Service with required dependencies
  const monitoringRepository = new MonitoringRepository();
  const auditTrailService = new AuditTrailService();
  const paimRepository = new PaimRepository(auditTrailService);
  const paimService = new PaimService(paimRepository, auditTrailService, notificationService, wsService);
  const powerOpsService = new PowerOpsService();
  const culturalSensitivityService = new CulturalSensitivityService(db);

  monitoringService = new MonitoringService(
    monitoringRepository,
    auditTrailService,
    paimService,
    powerOpsService,
    culturalSensitivityService
  );
  logger.info('Monitoring Service initialized.');

  // Initialize Workflow Collaboration Service and Controller with CollaborationEvents and CollaborationSessionService
  workflowCollaborationService = new WorkflowCollaborationService(
    workflowCollaborationRepository,
    collaborationEvents,
    collaborationSessionService // Pass the new service
  );
  workflowCollaborationController = new WorkflowCollaborationController(workflowCollaborationService);

  // Initialize PowerOps Controller with NotificationService
  powerOpsController = new PowerOpsController(notificationService);
  logger.info('PowerOps Controller initialized.');

  // Re-register Workflow and Collaboration Routes now that dependencies are initialized
  app.use('/api/v1/workflow-collaboration', workflowCollaborationController.router);
  logger.info('Registered /api/v1/workflow-collaboration routes');

  // Initialize Paim Controller with NotificationService and WebSocketService
  paimController = new PaimController(notificationService, wsService);
  logger.info('PAIM Controller initialized.');

  // Re-register PAIM Management Routes
  app.use('/api/v1/paim-instances', paimController.router);
  logger.info('Registered /api/v1/paim-instances routes');

  // Re-register PowerOps Gamification and Cost Management Routes
  app.use('/api/v1/powerops', powerOpsController.router);
  logger.info('Registered /api/v1/powerops routes');

});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;