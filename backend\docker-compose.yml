services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: theaigency-db
    environment:
      POSTGRES_DB: theaigency_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d theaigency_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - theaigency

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: theaigency-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - theaigency

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: theaigency-qdrant
    ports:
      - "6333:6333" # REST API
      - "6334:6334" # gRPC
    volumes:
      - qdrant_data:/qdrant/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - theaigency

  # Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: theaigency-backend
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************/theaigency_db
      REDIS_URL: redis://redis:6379
      QDRANT_URL: http://qdrant:6333
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      PORT: 3000
      # Appwrite Configuration
      APPWRITE_ENDPOINT: ${APPWRITE_ENDPOINT:-https://cloud.appwrite.io/v1}
      APPWRITE_PROJECT_ID: ${APPWRITE_PROJECT_ID:-your-project-id}
      APPWRITE_API_KEY: ${APPWRITE_API_KEY:-your-api-key}
      APPWRITE_DATABASE_ID: ${APPWRITE_DATABASE_ID:-your-database-id}
      # External API Keys (optional)
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:-}
      RUNWAYML_API_KEY: ${RUNWAYML_API_KEY:-}
      ADOBE_FIREFLY_API_KEY: ${ADOBE_FIREFLY_API_KEY:-}
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    volumes:
      - ./logs:/app/backend/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - theaigency

volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local

networks:
  theaigency:
    driver: bridge
