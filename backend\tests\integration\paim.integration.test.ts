import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import { setupTestDatabase, teardownTestDatabase, testDb } from './setup';
import { testUsers, testPaimInstances } from '../fixtures/testData';
import app from '../../src/app';
import { PaimTierEnum } from '../../src/types/db';

describe('PAIM Integration Tests', () => {
  let testApp: express.Application;
  let adminToken: string;
  let basicUserToken: string;
  let adminUser: any;
  let basicUser: any;

  beforeAll(async () => {
    await setupTestDatabase();
    testApp = app;

    // Create admin user and get token
    const adminRegisterResponse = await request(testApp)
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'AdminPassword123!',
        firstName: 'Admin',
        lastName: 'User',
      });
    
    adminToken = adminRegisterResponse.body.tokens.accessToken;
    adminUser = adminRegisterResponse.body.user;

    // Create basic user and get token
    const basicRegisterResponse = await request(testApp)
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'BasicPassword123!',
        firstName: 'Basic',
        lastName: 'User',
      });
    
    basicUserToken = basicRegisterResponse.body.tokens.accessToken;
    basicUser = basicRegisterResponse.body.user;
  });

  afterAll(async () => {
    await teardownTestDatabase();
  });

  beforeEach(async () => {
    // Clean up PAIM-related test data before each test
    if (testDb) {
      await testDb('paim_instances').del();
      await testDb('paim_tier_change_requests').del();
    }
  });

  describe('POST /paim-management/instances', () => {
    it('should successfully create a PAIM instance with admin permissions', async () => {
      const paimData = {
        name: 'Test PAIM Instance',
        tier: PaimTierEnum.Basic,
        configuration: {
          maxAgents: 5,
          features: ['basic_ai', 'task_management'],
        },
      };

      const response = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(paimData)
        .expect(201);

      expect(response.body).toMatchObject({
        id: expect.any(String),
        name: paimData.name,
        tier: paimData.tier,
        tenantId: adminUser.tenantId,
        status: 'active',
        configuration: paimData.configuration,
      });

      // Verify PAIM instance was created in database
      const createdPaim = await testDb('paim_instances').where({ id: response.body.id }).first();
      expect(createdPaim).toBeDefined();
      expect(createdPaim.name).toBe(paimData.name);
    });

    it('should return 403 for basic user without admin permissions', async () => {
      const paimData = {
        name: 'Unauthorized PAIM',
        tier: PaimTierEnum.Basic,
        configuration: {},
      };

      const response = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${basicUserToken}`)
        .send(paimData)
        .expect(403);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('Insufficient permissions'),
      });
    });

    it('should return 400 for invalid PAIM data', async () => {
      const invalidPaimData = {
        name: '', // Invalid: empty name
        tier: 'invalid_tier', // Invalid tier
      };

      const response = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidPaimData)
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('GET /paim-management/instances/:id', () => {
    let paimInstanceId: string;

    beforeEach(async () => {
      // Create a test PAIM instance
      const createResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test PAIM for Retrieval',
          tier: PaimTierEnum.Basic,
          configuration: { maxAgents: 5 },
        });
      
      paimInstanceId = createResponse.body.id;
    });

    it('should successfully retrieve a PAIM instance', async () => {
      const response = await request(testApp)
        .get(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: paimInstanceId,
        name: 'Test PAIM for Retrieval',
        tier: PaimTierEnum.Basic,
        tenantId: adminUser.tenantId,
      });
    });

    it('should return 404 for non-existent PAIM instance', async () => {
      const response = await request(testApp)
        .get('/paim-management/instances/nonexistent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('not found'),
      });
    });

    it('should return 401 for missing authorization', async () => {
      const response = await request(testApp)
        .get(`/paim-management/instances/${paimInstanceId}`)
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('PUT /paim-management/instances/:id', () => {
    let paimInstanceId: string;

    beforeEach(async () => {
      // Create a test PAIM instance
      const createResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test PAIM for Update',
          tier: PaimTierEnum.Basic,
          configuration: { maxAgents: 5 },
        });
      
      paimInstanceId = createResponse.body.id;
    });

    it('should successfully update a PAIM instance', async () => {
      const updateData = {
        name: 'Updated PAIM Instance',
        configuration: {
          maxAgents: 10,
          features: ['basic_ai', 'advanced_ai'],
        },
      };

      const response = await request(testApp)
        .put(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        id: paimInstanceId,
        name: updateData.name,
        configuration: updateData.configuration,
      });

      // Verify update in database
      const updatedPaim = await testDb('paim_instances').where({ id: paimInstanceId }).first();
      expect(updatedPaim.name).toBe(updateData.name);
    });

    it('should return 403 for basic user without permissions', async () => {
      const updateData = { name: 'Unauthorized Update' };

      const response = await request(testApp)
        .put(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${basicUserToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('Insufficient permissions'),
      });
    });
  });

  describe('POST /paim-management/instances/:id/tier-change-requests', () => {
    let paimInstanceId: string;

    beforeEach(async () => {
      // Create a test PAIM instance
      const createResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test PAIM for Tier Change',
          tier: PaimTierEnum.Basic,
          configuration: { maxAgents: 5 },
        });
      
      paimInstanceId = createResponse.body.id;
    });

    it('should successfully request a tier change', async () => {
      const tierChangeRequest = {
        requestedTier: PaimTierEnum.Enterprise,
        justification: 'Need more features for growing team',
      };

      const response = await request(testApp)
        .post(`/paim-management/instances/${paimInstanceId}/tier-change-requests`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(tierChangeRequest)
        .expect(202);

      expect(response.body).toMatchObject({
        id: expect.any(String),
        status: 'pending',
        requestedTier: PaimTierEnum.Enterprise,
        currentTier: PaimTierEnum.Basic,
        requestedAt: expect.any(String),
      });

      // Verify tier change request was created in database
      const tierChangeRecord = await testDb('paim_tier_change_requests')
        .where({ paim_instance_id: paimInstanceId })
        .first();
      expect(tierChangeRecord).toBeDefined();
      expect(tierChangeRecord.requested_tier).toBe(PaimTierEnum.Enterprise);
    });

    it('should return 400 for invalid tier change request', async () => {
      const invalidRequest = {
        requestedTier: 'invalid_tier',
        justification: '',
      };

      const response = await request(testApp)
        .post(`/paim-management/instances/${paimInstanceId}/tier-change-requests`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('GET /paim-management/instances/:id/hierarchy', () => {
    let paimInstanceId: string;

    beforeEach(async () => {
      // Create a test PAIM instance
      const createResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Root PAIM Instance',
          tier: PaimTierEnum.Enterprise,
          configuration: { maxAgents: 20 },
        });
      
      paimInstanceId = createResponse.body.id;
    });

    it('should successfully retrieve PAIM hierarchy', async () => {
      const response = await request(testApp)
        .get(`/paim-management/instances/${paimInstanceId}/hierarchy`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: paimInstanceId,
        name: 'Root PAIM Instance',
        children: expect.any(Array),
      });
    });

    it('should return 404 for non-existent PAIM instance', async () => {
      const response = await request(testApp)
        .get('/paim-management/instances/nonexistent-id/hierarchy')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('not found'),
      });
    });
  });

  describe('DELETE /paim-management/instances/:id', () => {
    let paimInstanceId: string;

    beforeEach(async () => {
      // Create a test PAIM instance
      const createResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test PAIM for Deletion',
          tier: PaimTierEnum.Basic,
          configuration: { maxAgents: 5 },
        });
      
      paimInstanceId = createResponse.body.id;
    });

    it('should successfully delete a PAIM instance', async () => {
      const response = await request(testApp)
        .delete(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('deleted'),
      });

      // Verify deletion in database
      const deletedPaim = await testDb('paim_instances').where({ id: paimInstanceId }).first();
      expect(deletedPaim).toBeUndefined();
    });

    it('should return 403 for basic user without permissions', async () => {
      const response = await request(testApp)
        .delete(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${basicUserToken}`)
        .expect(403);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('Insufficient permissions'),
      });
    });
  });
});
