import { WebSocket } from 'ws';
import jwt from 'jsonwebtoken';
import { Request } from 'express'; // Import Request from express for type augmentation
import { AuthenticatedRequest } from '../types/express-augmentation'; // Assuming this exists for user info

export interface WebSocketWithAuth extends WebSocket {
  userId?: string;
  isAuthenticated?: boolean;
}

export const authenticateWebSocket = (ws: WebSocketWithAuth, req: AuthenticatedRequest, next: Function) => {
  const token = (req.headers as any)['sec-websocket-protocol']; // Or from query params, depending on client
  if (!token) {
    ws.close(1008, 'Authentication token missing');
    return;
  }

  try {
    // Replace 'YOUR_JWT_SECRET' with your actual JWT secret from config
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'YOUR_JWT_SECRET') as { userId: string };
    ws.userId = decoded.userId;
    ws.isAuthenticated = true;
    next();
  } catch (error) {
    console.error('WebSocket authentication failed:', error);
    ws.close(1008, 'Authentication failed');
  }
};

export const authorizeWebSocket = (requiredRoles: string[]) => {
  return (ws: WebSocketWithAuth, req: AuthenticatedRequest, next: Function) => {
    // This is a placeholder. In a real app, you'd fetch user roles from DB
    // based on ws.userId and check against requiredRoles.
    // For now, we'll just assume authenticated users are authorized.
    if (ws.isAuthenticated) {
      console.log(`User ${ws.userId} is authorized for WebSocket connection.`);
      next();
    } else {
      ws.close(1008, 'Authorization failed: User not authenticated');
    }
  };
};