# Phase 4: Integration Testing & QC - Comprehensive Test Runner (PowerShell)
# This script runs all tests and generates reports for Phase 4 completion

param(
    [switch]$SkipDocker,
    [switch]$Verbose
)

# Error handling
$ErrorActionPreference = "Stop"

# Colors for output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Log($message) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-ColorOutput Blue "[$timestamp] $message"
}

function Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

# Create reports directory
New-Item -ItemType Directory -Force -Path "reports", "coverage", "logs" | Out-Null

Log "Starting Phase 4: Integration Testing & QC"
Log "============================================"

# Check if Docker is available
$DockerAvailable = $false
if (-not $SkipDocker) {
    try {
        docker --version | Out-Null
        $DockerAvailable = $true
        Log "Docker is available - will run containerized tests"
    } catch {
        Warning "Docker not available - running local tests only"
    }
}

# Function to run tests with error handling
function Run-TestSuite {
    param(
        [string]$TestName,
        [string]$TestCommand,
        [string]$LogFile = "logs/$TestName.log"
    )
    
    Log "Running $TestName tests..."
    
    try {
        if ($Verbose) {
            Invoke-Expression $TestCommand
        } else {
            Invoke-Expression "$TestCommand > $LogFile 2>&1"
        }
        Success "$TestName tests passed"
        return $true
    } catch {
        Error "$TestName tests failed - check $LogFile"
        if ($Verbose) {
            Write-Host $_.Exception.Message
        }
        return $false
    }
}

# Initialize test results
$TotalTests = 0
$PassedTests = 0
$FailedTests = 0

# Test 1: Unit Tests
Log "Phase 4.1: Running Unit Tests"
Log "-----------------------------"
$TotalTests++
if (Run-TestSuite "unit" "npm run test:unit") {
    $PassedTests++
} else {
    $FailedTests++
}

# Test 2: Integration Tests
Log "Phase 4.2: Running Integration Tests"
Log "------------------------------------"
$TotalTests++
if (Run-TestSuite "integration" "npm run test:integration") {
    $PassedTests++
} else {
    $FailedTests++
}

# Test 3: End-to-End Tests
Log "Phase 4.3: Running End-to-End Tests"
Log "-----------------------------------"
$TotalTests++
if (Run-TestSuite "e2e" "npm run test:e2e") {
    $PassedTests++
} else {
    $FailedTests++
}

# Test 4: Performance Tests
Log "Phase 4.4: Running Performance Tests"
Log "------------------------------------"
$TotalTests++
if (Run-TestSuite "performance" "npm run performance:test") {
    $PassedTests++
} else {
    $FailedTests++
}

# Test 5: Security Tests
Log "Phase 4.5: Running Security Tests"
Log "---------------------------------"
$TotalTests++
if (Run-TestSuite "security" "npm run security:test") {
    $PassedTests++
} else {
    $FailedTests++
}

# Test 6: Code Coverage
Log "Phase 4.6: Generating Code Coverage Report"
Log "------------------------------------------"
$TotalTests++
if (Run-TestSuite "coverage" "npm run test:coverage") {
    $PassedTests++
    
    # Check coverage threshold
    if (Test-Path "coverage/coverage-summary.json") {
        try {
            $coverage = Get-Content "coverage/coverage-summary.json" | ConvertFrom-Json
            $coveragePercent = [math]::Round($coverage.total.lines.pct)
            
            if ($coveragePercent -ge 80) {
                Success "Code coverage: $coveragePercent% (meets 80% requirement)"
            } else {
                Warning "Code coverage: $coveragePercent% (below 80% requirement)"
            }
        } catch {
            Warning "Could not parse coverage report"
        }
    }
} else {
    $FailedTests++
}

# Test 7: TypeScript Compilation (Strict Mode)
Log "Phase 4.7: Testing TypeScript Strict Mode Compilation"
Log "-----------------------------------------------------"
$TotalTests++
if (Run-TestSuite "typescript" "npm run build") {
    $PassedTests++
    Success "TypeScript strict mode compilation successful"
} else {
    $FailedTests++
}

# Test 8: Linting
Log "Phase 4.8: Running Code Quality Checks"
Log "--------------------------------------"
$TotalTests++
if (Run-TestSuite "lint" "npm run lint") {
    $PassedTests++
} else {
    $FailedTests++
}

# Test 9: Docker Build Test
if ($DockerAvailable) {
    Log "Phase 4.9: Testing Docker Build"
    Log "-------------------------------"
    $TotalTests++
    if (Run-TestSuite "docker-build" "docker build -t theaigency-backend-test .") {
        $PassedTests++
        Success "Docker build successful"
    } else {
        $FailedTests++
    }
    
    # Test 10: Docker Compose Test
    Log "Phase 4.10: Testing Docker Compose"
    Log "----------------------------------"
    $TotalTests++
    try {
        if (Run-TestSuite "docker-compose" "docker-compose -f docker-compose.test.yml up --abort-on-container-exit") {
            $PassedTests++
            Success "Docker Compose test successful"
        } else {
            $FailedTests++
        }
    } finally {
        # Cleanup
        try {
            docker-compose -f docker-compose.test.yml down -v | Out-Null
        } catch {
            Warning "Could not clean up Docker Compose"
        }
    }
}

# Generate final report
Log "Generating Phase 4 Test Report"
Log "==============================="

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$successRate = [math]::Round(($PassedTests * 100) / $TotalTests, 2)

$report = @{
    phase = "Phase 4: Integration Testing & QC"
    timestamp = $timestamp
    summary = @{
        totalTests = $TotalTests
        passedTests = $PassedTests
        failedTests = $FailedTests
        successRate = $successRate
    }
    testSuites = @{
        unit = if (Test-Path "logs/unit.log") { "completed" } else { "not_run" }
        integration = if (Test-Path "logs/integration.log") { "completed" } else { "not_run" }
        e2e = if (Test-Path "logs/e2e.log") { "completed" } else { "not_run" }
        performance = if (Test-Path "logs/performance.log") { "completed" } else { "not_run" }
        security = if (Test-Path "logs/security.log") { "completed" } else { "not_run" }
        coverage = if (Test-Path "logs/coverage.log") { "completed" } else { "not_run" }
        typescript = if (Test-Path "logs/typescript.log") { "completed" } else { "not_run" }
        lint = if (Test-Path "logs/lint.log") { "completed" } else { "not_run" }
    }
    dockerTests = @{
        available = $DockerAvailable
        build = if (Test-Path "logs/docker-build.log") { "completed" } else { "not_run" }
        compose = if (Test-Path "logs/docker-compose.log") { "completed" } else { "not_run" }
    }
    phase4Status = if ($FailedTests -eq 0) { "PASSED" } else { "FAILED" }
}

$report | ConvertTo-Json -Depth 3 | Out-File "reports/phase4-final-report.json" -Encoding UTF8

# Display final results
Log "Phase 4 Test Results Summary"
Log "============================"
Write-Host "Total Tests: $TotalTests"
Write-Host "Passed: $PassedTests"
Write-Host "Failed: $FailedTests"
Write-Host "Success Rate: $successRate%"
Write-Host ""

if ($FailedTests -eq 0) {
    Success "🎉 Phase 4: Integration Testing & QC COMPLETED SUCCESSFULLY!"
    Success "✅ All test suites passed"
    Success "✅ Code coverage meets requirements"
    Success "✅ TypeScript strict mode enabled"
    Success "✅ Docker configuration optimized"
    Success "✅ Ready for production deployment"
    Write-Host ""
    Success "Phase 4 Status: READY FOR DOCKER LAUNCH"
    exit 0
} else {
    Error "❌ Phase 4: Integration Testing & QC FAILED"
    Error "Some test suites failed. Please check the logs in the logs/ directory."
    Write-Host ""
    Error "Phase 4 Status: REQUIRES FIXES BEFORE LAUNCH"
    exit 1
}
