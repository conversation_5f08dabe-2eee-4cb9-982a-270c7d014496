import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import { setupTestDatabase, teardownTestDatabase, testDb } from './setup';
import { testUsers } from '../fixtures/testData';
import app from '../../src/app';

describe('Authentication Integration Tests', () => {
  let testApp: express.Application;

  beforeAll(async () => {
    await setupTestDatabase();
    testApp = app;
  });

  afterAll(async () => {
    await teardownTestDatabase();
  });

  beforeEach(async () => {
    // Clean up test data before each test
    if (testDb) {
      await testDb('users').del();
      await testDb('sessions').del();
    }
  });

  describe('POST /auth/register', () => {
    it('should successfully register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      const response = await request(testApp)
        .post('/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        user: expect.objectContaining({
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
        }),
        tokens: expect.objectContaining({
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        }),
      });

      // Verify user was created in database
      const createdUser = await testDb('users').where({ email: userData.email }).first();
      expect(createdUser).toBeDefined();
      expect(createdUser.email).toBe(userData.email);
    });

    it('should return 400 for invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      const response = await request(testApp)
        .post('/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('email'),
      });
    });

    it('should return 400 for weak password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123', // Too weak
        firstName: 'John',
        lastName: 'Doe',
      };

      const response = await request(testApp)
        .post('/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('password'),
      });
    });

    it('should return 409 for duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      // First registration should succeed
      await request(testApp)
        .post('/auth/register')
        .send(userData)
        .expect(201);

      // Second registration with same email should fail
      const response = await request(testApp)
        .post('/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('already exists'),
      });
    });
  });

  describe('POST /auth/login', () => {
    beforeEach(async () => {
      // Create a test user for login tests
      await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          firstName: 'Test',
          lastName: 'User',
        });
    });

    it('should successfully login with valid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
      };

      const response = await request(testApp)
        .post('/auth/login')
        .send(credentials)
        .expect(200);

      expect(response.body).toMatchObject({
        user: expect.objectContaining({
          email: credentials.email,
        }),
        tokens: expect.objectContaining({
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        }),
      });

      // Verify session was created
      const session = await testDb('sessions').where({ user_id: response.body.user.id }).first();
      expect(session).toBeDefined();
    });

    it('should return 401 for invalid email', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
      };

      const response = await request(testApp)
        .post('/auth/login')
        .send(credentials)
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('Invalid credentials'),
      });
    });

    it('should return 401 for invalid password', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'WrongPassword123!',
      };

      const response = await request(testApp)
        .post('/auth/login')
        .send(credentials)
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('Invalid credentials'),
      });
    });

    it('should return 400 for missing credentials', async () => {
      const response = await request(testApp)
        .post('/auth/login')
        .send({})
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('POST /auth/refresh', () => {
    let refreshToken: string;
    let userId: string;

    beforeEach(async () => {
      // Register and login to get refresh token
      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          firstName: 'Refresh',
          lastName: 'User',
        });

      refreshToken = registerResponse.body.tokens.refreshToken;
      userId = registerResponse.body.user.id;
    });

    it('should successfully refresh access token', async () => {
      const response = await request(testApp)
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body).toMatchObject({
        accessToken: expect.any(String),
        user: expect.objectContaining({
          id: userId,
        }),
      });
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await request(testApp)
        .post('/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
        message: expect.stringContaining('Invalid'),
      });
    });

    it('should return 400 for missing refresh token', async () => {
      const response = await request(testApp)
        .post('/auth/refresh')
        .send({})
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('POST /auth/logout', () => {
    let accessToken: string;
    let refreshToken: string;

    beforeEach(async () => {
      // Register and login to get tokens
      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          firstName: 'Logout',
          lastName: 'User',
        });

      accessToken = registerResponse.body.tokens.accessToken;
      refreshToken = registerResponse.body.tokens.refreshToken;
    });

    it('should successfully logout user', async () => {
      const response = await request(testApp)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ refreshToken })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Logged out'),
      });

      // Verify session was deleted
      const session = await testDb('sessions').where({ refresh_token: refreshToken }).first();
      expect(session).toBeUndefined();
    });

    it('should return 401 for missing authorization', async () => {
      const response = await request(testApp)
        .post('/auth/logout')
        .send({ refreshToken })
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('GET /auth/me', () => {
    let accessToken: string;
    let user: any;

    beforeEach(async () => {
      // Register and login to get access token
      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          firstName: 'Me',
          lastName: 'User',
        });

      accessToken = registerResponse.body.tokens.accessToken;
      user = registerResponse.body.user;
    });

    it('should successfully get current user info', async () => {
      const response = await request(testApp)
        .get('/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      });
    });

    it('should return 401 for missing authorization', async () => {
      const response = await request(testApp)
        .get('/auth/me')
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });

    it('should return 401 for invalid token', async () => {
      const response = await request(testApp)
        .get('/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });
});
