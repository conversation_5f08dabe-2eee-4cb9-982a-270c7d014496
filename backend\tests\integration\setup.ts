import dotenv from 'dotenv';
import { vi } from 'vitest';
import knex from 'knex';
import { config } from '../../src/config';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set NODE_ENV for tests
process.env.NODE_ENV = 'test';

// Test database configuration
const testDbConfig = {
  client: 'sqlite3',
  connection: {
    filename: ':memory:', // Use in-memory SQLite for fast tests
  },
  useNullAsDefault: true,
  migrations: {
    directory: './migrations',
  },
  seeds: {
    directory: './seeds',
  },
};

// Global test database instance
let testDb: any;

// Setup function to run before all tests
export async function setupTestDatabase() {
  testDb = knex(testDbConfig);
  
  // Run migrations
  await testDb.migrate.latest();
  
  // Run seeds for test data
  await testDb.seed.run();
  
  return testDb;
}

// Cleanup function to run after all tests
export async function teardownTestDatabase() {
  if (testDb) {
    await testDb.destroy();
  }
}

// Mock external services
vi.mock('node-appwrite', () => ({
  Client: vi.fn(() => ({
    setEndpoint: vi.fn().mockReturnThis(),
    setProject: vi.fn().mockReturnThis(),
    setKey: vi.fn().mockReturnThis(),
  })),
  Account: vi.fn(() => ({
    get: vi.fn().mockResolvedValue({
      $id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
    }),
  })),
  Databases: vi.fn(() => ({
    listDocuments: vi.fn().mockResolvedValue({ documents: [] }),
    createDocument: vi.fn().mockResolvedValue({ $id: 'test-doc-id' }),
    updateDocument: vi.fn().mockResolvedValue({ $id: 'test-doc-id' }),
    deleteDocument: vi.fn().mockResolvedValue({}),
  })),
  Users: vi.fn(() => ({
    list: vi.fn().mockResolvedValue({ users: [] }),
    create: vi.fn().mockResolvedValue({ $id: 'test-user-id' }),
  })),
}));

// Mock Redis
vi.mock('redis', () => ({
  createClient: vi.fn(() => ({
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn().mockResolvedValue(undefined),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
  })),
}));

// Mock bcryptjs
vi.mock('bcryptjs', () => ({
  hash: vi.fn().mockResolvedValue('hashed-password'),
  compare: vi.fn().mockResolvedValue(true),
}));

// Mock JWT
vi.mock('jsonwebtoken', () => ({
  sign: vi.fn().mockReturnValue('test-jwt-token'),
  verify: vi.fn().mockReturnValue({
    userId: 'test-user-id',
    tenantId: 'test-tenant-id',
    email: '<EMAIL>',
    paimTier: 'enterprise',
    roles: ['user'],
  }),
}));

// Export test utilities
export { testDb };
export const testUser = {
  userId: 'test-user-id',
  tenantId: 'test-tenant-id',
  email: '<EMAIL>',
  paimTier: 'enterprise',
  roles: ['user'],
};

export const testAuthToken = 'Bearer test-jwt-token';
