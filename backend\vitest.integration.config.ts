import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,
    include: ['tests/integration/**/*.test.ts'],
    testTimeout: 30000, // 30 seconds for integration tests
    setupFiles: ['./tests/integration/setup.ts'],
    pool: 'forks', // Use separate processes for integration tests
    poolOptions: {
      forks: {
        singleFork: true, // Run tests sequentially to avoid database conflicts
      },
    },
  },
  resolve: {
    alias: {
      '@config': path.resolve(__dirname, './src/config'),
      '@database': path.resolve(__dirname, './src/database'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
      '@auth': path.resolve(__dirname, './src/auth'),
      '@paim': path.resolve(__dirname, './src/paim'),
      '@powerops': path.resolve(__dirname, './src/powerops'),
      '@organization': path.resolve(__dirname, './src/organization'),
      '@aigency': path.resolve(__dirname, './src/aigency'),
      '@middleware': path.resolve(__dirname, './src/middleware'),
    },
  },
});
