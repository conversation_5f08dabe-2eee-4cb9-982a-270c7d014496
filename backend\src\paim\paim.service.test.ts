import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PaimService } from './paim.service';
import { PaimRepository } from './paim.repository';
import { AuthorizationError, CustomError } from '../utils/errors';
import { testUsers, testPaimInstances } from '../../tests/fixtures/testData';
import { PaimTierEnum } from '../types/db';

// Mock dependencies
vi.mock('./paim.repository');
vi.mock('../auth/authorization.service');

describe('PaimService', () => {
  let paimService: PaimService;
  let mockPaimRepository: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockPaimRepository = {
      createPaimInstance: vi.fn(),
      getPaimInstanceById: vi.fn(),
      getPaimInstancesByTenant: vi.fn(),
      updatePaimInstance: vi.fn(),
      deletePaimInstance: vi.fn(),
      getPaimHierarchy: vi.fn(),
      updatePaimHierarchy: vi.fn(),
      requestTierChange: vi.fn(),
      getTierChangeRequests: vi.fn(),
      approveTierChange: vi.fn(),
    };
    
    (PaimRepository as any).mockImplementation(() => mockPaimRepository);
    paimService = new PaimService();
  });

  describe('createPaimInstance', () => {
    it('should successfully create a PAIM instance', async () => {
      const user = testUsers.adminUser;
      const paimData = {
        name: 'Test PAIM Instance',
        tier: PaimTierEnum.Basic,
        configuration: {
          maxAgents: 5,
          features: ['basic_ai'],
        },
      };
      const mockCreatedPaim = {
        ...testPaimInstances.basicPaim,
        ...paimData,
      };

      mockPaimRepository.createPaimInstance.mockResolvedValue(mockCreatedPaim);

      const result = await paimService.createPaimInstance(user, paimData);

      expect(mockPaimRepository.createPaimInstance).toHaveBeenCalledWith({
        ...paimData,
        tenantId: user.tenantId,
        status: 'active',
      });
      expect(result).toEqual(mockCreatedPaim);
    });

    it('should throw AuthorizationError for insufficient permissions', async () => {
      const user = testUsers.basicUser; // Basic user without admin permissions
      const paimData = {
        name: 'Test PAIM Instance',
        tier: PaimTierEnum.Enterprise,
        configuration: {},
      };

      await expect(paimService.createPaimInstance(user, paimData)).rejects.toThrow(AuthorizationError);
      expect(mockPaimRepository.createPaimInstance).not.toHaveBeenCalled();
    });
  });

  describe('getPaimInstance', () => {
    it('should successfully retrieve a PAIM instance', async () => {
      const user = testUsers.basicUser;
      const paimInstanceId = 'paim-1';
      const mockPaim = testPaimInstances.basicPaim;

      mockPaimRepository.getPaimInstanceById.mockResolvedValue(mockPaim);

      const result = await paimService.getPaimInstance(paimInstanceId, user);

      expect(mockPaimRepository.getPaimInstanceById).toHaveBeenCalledWith(paimInstanceId, user.tenantId);
      expect(result).toEqual(mockPaim);
    });

    it('should throw CustomError when PAIM instance not found', async () => {
      const user = testUsers.basicUser;
      const paimInstanceId = 'nonexistent-paim';

      mockPaimRepository.getPaimInstanceById.mockResolvedValue(null);

      await expect(paimService.getPaimInstance(paimInstanceId, user)).rejects.toThrow(CustomError);
      expect(mockPaimRepository.getPaimInstanceById).toHaveBeenCalledWith(paimInstanceId, user.tenantId);
    });
  });

  describe('updatePaimInstance', () => {
    it('should successfully update a PAIM instance', async () => {
      const user = testUsers.adminUser;
      const paimInstanceId = 'paim-1';
      const updateData = {
        name: 'Updated PAIM Instance',
        configuration: {
          maxAgents: 10,
          features: ['basic_ai', 'advanced_ai'],
        },
      };
      const existingPaim = testPaimInstances.basicPaim;
      const updatedPaim = { ...existingPaim, ...updateData };

      mockPaimRepository.getPaimInstanceById.mockResolvedValue(existingPaim);
      mockPaimRepository.updatePaimInstance.mockResolvedValue(updatedPaim);

      const result = await paimService.updatePaimInstance(paimInstanceId, user, updateData);

      expect(mockPaimRepository.getPaimInstanceById).toHaveBeenCalledWith(paimInstanceId, user.tenantId);
      expect(mockPaimRepository.updatePaimInstance).toHaveBeenCalledWith(paimInstanceId, updateData);
      expect(result).toEqual(updatedPaim);
    });

    it('should throw CustomError when trying to update non-existent PAIM instance', async () => {
      const user = testUsers.adminUser;
      const paimInstanceId = 'nonexistent-paim';
      const updateData = { name: 'Updated Name' };

      mockPaimRepository.getPaimInstanceById.mockResolvedValue(null);

      await expect(paimService.updatePaimInstance(paimInstanceId, user, updateData)).rejects.toThrow(CustomError);
      expect(mockPaimRepository.updatePaimInstance).not.toHaveBeenCalled();
    });
  });

  describe('requestPaimTierChange', () => {
    it('should successfully request a tier change', async () => {
      const user = testUsers.adminUser;
      const paimInstanceId = 'paim-1';
      const tierChangeRequest = {
        requestedTier: PaimTierEnum.Enterprise,
        justification: 'Need more features for growing team',
      };
      const existingPaim = testPaimInstances.basicPaim;
      const mockTierChangeStatus = {
        id: 'request-1',
        status: 'pending',
        requestedTier: PaimTierEnum.Enterprise,
        currentTier: PaimTierEnum.Basic,
        requestedAt: new Date(),
      };

      mockPaimRepository.getPaimInstanceById.mockResolvedValue(existingPaim);
      mockPaimRepository.requestTierChange.mockResolvedValue(mockTierChangeStatus);

      const result = await paimService.requestPaimTierChange(paimInstanceId, user, tierChangeRequest);

      expect(mockPaimRepository.getPaimInstanceById).toHaveBeenCalledWith(paimInstanceId, user.tenantId);
      expect(mockPaimRepository.requestTierChange).toHaveBeenCalledWith(paimInstanceId, tierChangeRequest);
      expect(result).toEqual(mockTierChangeStatus);
    });

    it('should throw AuthorizationError for insufficient permissions', async () => {
      const user = testUsers.basicUser; // Basic user without tier change permissions
      const paimInstanceId = 'paim-1';
      const tierChangeRequest = {
        requestedTier: PaimTierEnum.Enterprise,
        justification: 'Need more features',
      };

      await expect(paimService.requestPaimTierChange(paimInstanceId, user, tierChangeRequest)).rejects.toThrow(AuthorizationError);
      expect(mockPaimRepository.requestTierChange).not.toHaveBeenCalled();
    });
  });

  describe('getPaimHierarchy', () => {
    it('should successfully retrieve PAIM hierarchy', async () => {
      const user = testUsers.basicUser;
      const paimInstanceId = 'paim-1';
      const mockHierarchy = {
        id: paimInstanceId,
        name: 'Root PAIM',
        children: [
          { id: 'child-1', name: 'Child PAIM 1', children: [] },
          { id: 'child-2', name: 'Child PAIM 2', children: [] },
        ],
      };

      mockPaimRepository.getPaimHierarchy.mockResolvedValue(mockHierarchy);

      const result = await paimService.getPaimHierarchy(paimInstanceId, user);

      expect(mockPaimRepository.getPaimHierarchy).toHaveBeenCalledWith(paimInstanceId, user.tenantId);
      expect(result).toEqual(mockHierarchy);
    });
  });

  describe('deletePaimInstance', () => {
    it('should successfully delete a PAIM instance', async () => {
      const user = testUsers.adminUser;
      const paimInstanceId = 'paim-1';
      const existingPaim = testPaimInstances.basicPaim;

      mockPaimRepository.getPaimInstanceById.mockResolvedValue(existingPaim);
      mockPaimRepository.deletePaimInstance.mockResolvedValue(true);

      const result = await paimService.deletePaimInstance(paimInstanceId, user);

      expect(mockPaimRepository.getPaimInstanceById).toHaveBeenCalledWith(paimInstanceId, user.tenantId);
      expect(mockPaimRepository.deletePaimInstance).toHaveBeenCalledWith(paimInstanceId);
      expect(result).toBe(true);
    });

    it('should throw AuthorizationError for insufficient permissions', async () => {
      const user = testUsers.basicUser; // Basic user without delete permissions
      const paimInstanceId = 'paim-1';

      await expect(paimService.deletePaimInstance(paimInstanceId, user)).rejects.toThrow(AuthorizationError);
      expect(mockPaimRepository.deletePaimInstance).not.toHaveBeenCalled();
    });
  });

  describe('communicateWithPaim', () => {
    it('should successfully facilitate cross-PAIM communication', async () => {
      const user = testUsers.adminUser;
      const sourcePaimInstanceId = 'paim-1';
      const targetPaimInstanceId = 'paim-2';
      const message = 'Hello from PAIM 1';
      const messageType = 'greeting';

      const mockCommunicationResult = {
        success: true,
        messageId: 'msg-1',
        timestamp: new Date(),
      };

      // Mock the communication method (placeholder implementation)
      const result = await paimService.communicateWithPaim(sourcePaimInstanceId, user, targetPaimInstanceId, message, messageType);

      expect(result).toBeDefined();
      // Add more specific assertions based on actual implementation
    });

    it('should throw AuthorizationError for insufficient permissions', async () => {
      const user = testUsers.basicUser; // Basic user without cross-PAIM communication permissions
      const sourcePaimInstanceId = 'paim-1';
      const targetPaimInstanceId = 'paim-2';
      const message = 'Hello';
      const messageType = 'greeting';

      await expect(paimService.communicateWithPaim(sourcePaimInstanceId, user, targetPaimInstanceId, message, messageType)).rejects.toThrow(AuthorizationError);
    });
  });
});
