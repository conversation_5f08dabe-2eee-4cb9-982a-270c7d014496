{"version": "3.1.4", "results": [[":src/utils/http-client.service.test.ts", {"duration": 0, "failed": true}], [":tests/e2e/complete-workflow.e2e.test.ts", {"duration": 0, "failed": true}], [":src/organization/organization.service.test.ts", {"duration": 42.61279999999988, "failed": true}], [":src/utils/http-client.validation.test.ts", {"duration": 70121.159, "failed": true}], [":tests/integration/paim.integration.test.ts", {"duration": 0, "failed": true}], [":src/organization/organization.controller.test.ts", {"duration": 0, "failed": true}], [":src/powerops/powerops.repository.test.ts", {"duration": 35.88109999999983, "failed": true}], [":tests/security/auth.security.test.ts", {"duration": 0, "failed": true}], [":src/database/adapters/appwrite-adapter.test.ts", {"duration": 0, "failed": true}], [":tests/integration/auth.integration.test.ts", {"duration": 0, "failed": true}], [":src/powerops/powerops.controller.test.ts", {"duration": 0, "failed": true}], [":tests/performance/api.performance.test.ts", {"duration": 0, "failed": true}], [":src/paim/paim.service.test.ts", {"duration": 32.08510000000001, "failed": true}], [":src/agent-framework/integration.test.ts", {"duration": 298.32000000000005, "failed": true}], [":src/creative-api/creative-api.service.test.ts", {"duration": 1263.2032, "failed": false}], [":src/billing/billing.service.test.ts", {"duration": 36.58480000000009, "failed": true}], [":src/agent-framework/core/Agent.test.ts", {"duration": 45.56389999999999, "failed": true}], [":src/organization/organization.repository.test.ts", {"duration": 17.233499999999992, "failed": false}], [":src/auth/auth.service.test.ts", {"duration": 31.09540000000004, "failed": true}], [":src/billing/billing.repository.test.ts", {"duration": 24.977300000000014, "failed": true}], [":src/powerops/powerops.service.test.ts", {"duration": 20.462499999999864, "failed": true}], [":src/monitoring/monitoring.controller.test.ts", {"duration": 18.945900000000165, "failed": true}], [":src/billing/billing.integration.test.ts", {"duration": 0, "failed": true}]]}