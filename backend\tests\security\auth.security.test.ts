import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { setupTestDatabase, teardownTestDatabase } from '../integration/setup';
import app from '../../src/app';

describe('Authentication Security Tests', () => {
  let testApp: any;

  beforeAll(async () => {
    await setupTestDatabase();
    testApp = app;
  });

  afterAll(async () => {
    await teardownTestDatabase();
  });

  describe('Password Security', () => {
    it('should reject weak passwords', async () => {
      const weakPasswords = [
        '123',
        'password',
        '12345678',
        'qwerty',
        'abc123',
        'password123',
      ];

      for (const weakPassword of weakPasswords) {
        const response = await request(testApp)
          .post('/auth/register')
          .send({
            email: `test${Date.now()}@example.com`,
            password: weakPassword,
            firstName: 'Test',
            lastName: 'User',
          })
          .expect(400);

        expect(response.body).toMatchObject({
          error: expect.any(String),
          message: expect.stringMatching(/password/i),
        });
      }
    });

    it('should accept strong passwords', async () => {
      const strongPasswords = [
        'StrongPassword123!',
        'MySecure@Pass2024',
        'Complex#Password$456',
      ];

      for (const strongPassword of strongPasswords) {
        const response = await request(testApp)
          .post('/auth/register')
          .send({
            email: `strong${Date.now()}@example.com`,
            password: strongPassword,
            firstName: 'Test',
            lastName: 'User',
          })
          .expect(201);

        expect(response.body).toMatchObject({
          user: expect.any(Object),
          tokens: expect.any(Object),
        });
      }
    });

    it('should hash passwords securely', async () => {
      const password = 'TestPassword123!';
      const email = `hash${Date.now()}@example.com`;

      await request(testApp)
        .post('/auth/register')
        .send({
          email,
          password,
          firstName: 'Test',
          lastName: 'User',
        })
        .expect(201);

      // Verify password is not stored in plain text
      // This would require database access to verify the hash
      // For now, we'll verify that login works with the original password
      const loginResponse = await request(testApp)
        .post('/auth/login')
        .send({ email, password })
        .expect(200);

      expect(loginResponse.body.user.password).toBeUndefined();
    });
  });

  describe('JWT Token Security', () => {
    let validToken: string;
    let user: any;

    beforeAll(async () => {
      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'JWTTestPassword123!',
          firstName: 'JWT',
          lastName: 'Test',
        });

      validToken = registerResponse.body.tokens.accessToken;
      user = registerResponse.body.user;
    });

    it('should reject requests without authorization header', async () => {
      const response = await request(testApp)
        .get('/auth/me')
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });

    it('should reject malformed authorization headers', async () => {
      const malformedHeaders = [
        'InvalidToken',
        'Bearer',
        'Bearer ',
        'Basic dGVzdDp0ZXN0',
        'Bearer invalid.token.format',
      ];

      for (const header of malformedHeaders) {
        const response = await request(testApp)
          .get('/auth/me')
          .set('Authorization', header)
          .expect(401);

        expect(response.body).toMatchObject({
          error: expect.any(String),
        });
      }
    });

    it('should reject expired tokens', async () => {
      // This test would require creating an expired token
      // For now, we'll test with an obviously invalid token
      const expiredToken = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';

      const response = await request(testApp)
        .get('/auth/me')
        .set('Authorization', expiredToken)
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });

    it('should accept valid tokens', async () => {
      const response = await request(testApp)
        .get('/auth/me')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: user.id,
        email: user.email,
      });
    });

    it('should not expose sensitive information in JWT payload', async () => {
      // Decode the JWT payload (without verification for testing)
      const payload = JSON.parse(
        Buffer.from(validToken.split('.')[1], 'base64').toString()
      );

      // Ensure sensitive information is not in the token
      expect(payload.password).toBeUndefined();
      expect(payload.hashedPassword).toBeUndefined();
      expect(payload.salt).toBeUndefined();
    });
  });

  describe('Rate Limiting', () => {
    it('should rate limit login attempts', async () => {
      const email = `ratelimit${Date.now()}@example.com`;
      const password = 'RateLimitTest123!';

      // Register user first
      await request(testApp)
        .post('/auth/register')
        .send({
          email,
          password,
          firstName: 'Rate',
          lastName: 'Limit',
        })
        .expect(201);

      // Make multiple failed login attempts
      const failedAttempts = 10;
      const promises = Array.from({ length: failedAttempts }, () =>
        request(testApp)
          .post('/auth/login')
          .send({
            email,
            password: 'wrongpassword',
          })
      );

      const responses = await Promise.all(promises);

      // Some requests should be rate limited (429 status)
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should rate limit registration attempts', async () => {
      const attempts = 20;
      const promises = Array.from({ length: attempts }, (_, index) =>
        request(testApp)
          .post('/auth/register')
          .send({
            email: `spam${index}${Date.now()}@example.com`,
            password: 'SpamTest123!',
            firstName: 'Spam',
            lastName: 'Test',
          })
      );

      const responses = await Promise.all(promises);

      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should reject SQL injection attempts in email field', async () => {
      const sqlInjectionAttempts = [
        "<EMAIL>'; DROP TABLE users; --",
        "<EMAIL>' OR '1'='1",
        "<EMAIL>'; INSERT INTO users VALUES ('hacker', 'password'); --",
      ];

      for (const maliciousEmail of sqlInjectionAttempts) {
        const response = await request(testApp)
          .post('/auth/register')
          .send({
            email: maliciousEmail,
            password: 'TestPassword123!',
            firstName: 'Test',
            lastName: 'User',
          })
          .expect(400);

        expect(response.body).toMatchObject({
          error: expect.any(String),
        });
      }
    });

    it('should reject XSS attempts in name fields', async () => {
      const xssAttempts = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")',
        '<svg onload="alert(1)">',
      ];

      for (const maliciousName of xssAttempts) {
        const response = await request(testApp)
          .post('/auth/register')
          .send({
            email: `xss${Date.now()}@example.com`,
            password: 'TestPassword123!',
            firstName: maliciousName,
            lastName: 'User',
          })
          .expect(400);

        expect(response.body).toMatchObject({
          error: expect.any(String),
        });
      }
    });

    it('should handle extremely long input gracefully', async () => {
      const longString = 'a'.repeat(10000);

      const response = await request(testApp)
        .post('/auth/register')
        .send({
          email: `long${Date.now()}@example.com`,
          password: 'TestPassword123!',
          firstName: longString,
          lastName: 'User',
        })
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('Session Security', () => {
    let authToken: string;

    beforeAll(async () => {
      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SessionTest123!',
          firstName: 'Session',
          lastName: 'Test',
        });

      authToken = registerResponse.body.tokens.accessToken;
    });

    it('should invalidate session on logout', async () => {
      // Logout
      await request(testApp)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Try to use the token after logout
      const response = await request(testApp)
        .get('/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.any(String),
      });
    });
  });

  describe('CORS Security', () => {
    it('should include proper CORS headers', async () => {
      const response = await request(testApp)
        .options('/auth/login')
        .set('Origin', 'https://example.com')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toBeDefined();
      expect(response.headers['access-control-allow-headers']).toBeDefined();
    });

    it('should reject requests from unauthorized origins', async () => {
      // This test depends on CORS configuration
      // For now, we'll just verify that CORS headers are present
      const response = await request(testApp)
        .get('/health')
        .set('Origin', 'https://malicious-site.com');

      // The response should still include CORS headers
      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });
  });
});
