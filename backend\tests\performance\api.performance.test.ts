import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { setupTestDatabase, teardownTestDatabase } from '../integration/setup';
import app from '../../src/app';

describe('API Performance Tests', () => {
  let testApp: any;
  let authToken: string;

  beforeAll(async () => {
    await setupTestDatabase();
    testApp = app;

    // Register and login to get auth token
    const registerResponse = await request(testApp)
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'PerfTestPassword123!',
        firstName: 'Perf',
        lastName: 'Test',
      });
    
    authToken = registerResponse.body.tokens.accessToken;
  });

  afterAll(async () => {
    await teardownTestDatabase();
  });

  describe('Health Check Performance', () => {
    it('should respond to health check within 50ms', async () => {
      const startTime = Date.now();
      
      const response = await request(testApp)
        .get('/health')
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(50);
      expect(response.body).toMatchObject({
        status: 'ok',
        timestamp: expect.any(String),
      });
    });

    it('should handle 100 concurrent health check requests', async () => {
      const startTime = Date.now();
      const concurrentRequests = 100;
      
      const promises = Array.from({ length: concurrentRequests }, () =>
        request(testApp).get('/health').expect(200)
      );
      
      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      expect(responses).toHaveLength(concurrentRequests);
      expect(totalTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.body.status).toBe('ok');
      });
    });
  });

  describe('Authentication Performance', () => {
    it('should complete login within 200ms', async () => {
      const startTime = Date.now();
      
      const response = await request(testApp)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'PerfTestPassword123!',
        })
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(200);
      expect(response.body).toMatchObject({
        user: expect.any(Object),
        tokens: expect.any(Object),
      });
    });

    it('should handle 50 concurrent login requests', async () => {
      const startTime = Date.now();
      const concurrentRequests = 50;
      
      const promises = Array.from({ length: concurrentRequests }, () =>
        request(testApp)
          .post('/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'PerfTestPassword123!',
          })
          .expect(200)
      );
      
      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      expect(responses).toHaveLength(concurrentRequests);
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.body.user).toBeDefined();
        expect(response.body.tokens).toBeDefined();
      });
    });
  });

  describe('PAIM Management Performance', () => {
    it('should create PAIM instance within 300ms', async () => {
      const startTime = Date.now();
      
      const response = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Performance Test PAIM',
          tier: 'basic',
          configuration: { maxAgents: 5 },
        })
        .expect(201);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(300);
      expect(response.body).toMatchObject({
        id: expect.any(String),
        name: 'Performance Test PAIM',
      });
    });

    it('should retrieve PAIM instance within 100ms', async () => {
      // First create a PAIM instance
      const createResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Retrieval Test PAIM',
          tier: 'basic',
          configuration: { maxAgents: 5 },
        });
      
      const paimId = createResponse.body.id;
      
      const startTime = Date.now();
      
      const response = await request(testApp)
        .get(`/paim-management/instances/${paimId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(100);
      expect(response.body.id).toBe(paimId);
    });
  });

  describe('PowerOps Performance', () => {
    it('should log PowerOps usage within 150ms', async () => {
      const startTime = Date.now();
      
      const response = await request(testApp)
        .post('/powerops/usage')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          operation: 'ai_generation',
          resourceType: 'text',
          quantity: 1000,
          cost: 0.05,
        })
        .expect(201);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(150);
      expect(response.body).toMatchObject({
        id: expect.any(String),
        operation: 'ai_generation',
      });
    });

    it('should retrieve PowerOps usage within 200ms', async () => {
      const startTime = Date.now();
      
      const response = await request(testApp)
        .get('/powerops/usage')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(200);
      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should handle bulk XP awards efficiently', async () => {
      const startTime = Date.now();
      const bulkRequests = 20;
      
      const promises = Array.from({ length: bulkRequests }, (_, index) =>
        request(testApp)
          .post('/gamification/xp/add')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            entityId: `user-${index}`,
            entityType: 'user',
            amount: 100,
            reason: `Bulk test ${index}`,
          })
          .expect(201)
      );
      
      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      expect(responses).toHaveLength(bulkRequests);
      expect(totalTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      // All responses should be successful
      responses.forEach((response, index) => {
        expect(response.body).toMatchObject({
          entityId: `user-${index}`,
          amount: 100,
        });
      });
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should not leak memory during repeated requests', async () => {
      const initialMemory = process.memoryUsage();
      const requestCount = 100;
      
      // Make repeated requests
      for (let i = 0; i < requestCount; i++) {
        await request(testApp)
          .get('/health')
          .expect(200);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Memory increase should be reasonable (less than 50MB for 100 requests)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle large payloads efficiently', async () => {
      const largeConfiguration = {
        maxAgents: 1000,
        features: Array.from({ length: 100 }, (_, i) => `feature_${i}`),
        metadata: Array.from({ length: 50 }, (_, i) => ({
          key: `key_${i}`,
          value: `value_${i}`.repeat(100), // Large string values
        })),
      };
      
      const startTime = Date.now();
      
      const response = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Large Payload PAIM',
          tier: 'enterprise',
          configuration: largeConfiguration,
        })
        .expect(201);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(1000); // Should handle large payload within 1 second
      expect(response.body.configuration).toEqual(largeConfiguration);
    });
  });

  describe('Database Performance', () => {
    it('should handle database queries efficiently', async () => {
      // Create multiple PAIM instances to test query performance
      const createPromises = Array.from({ length: 10 }, (_, index) =>
        request(testApp)
          .post('/paim-management/instances')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            name: `DB Test PAIM ${index}`,
            tier: 'basic',
            configuration: { maxAgents: 5 },
          })
      );
      
      await Promise.all(createPromises);
      
      const startTime = Date.now();
      
      // Query all instances
      const response = await request(testApp)
        .get('/paim-management/instances')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      
      expect(responseTime).toBeLessThan(300);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThanOrEqual(10);
    });
  });
});
