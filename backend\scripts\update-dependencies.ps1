# Update Dependencies Script
# This script updates all dependencies to their latest stable versions

Write-Host "Updating TheAIgency Backend Dependencies..." -ForegroundColor Green

# Remove deprecated packages and update to latest versions
Write-Host "Removing deprecated packages..." -ForegroundColor Yellow

# Remove deprecated type packages
npm uninstall @types/express-rate-limit @types/jest @types/stripe

Write-Host "Updating core dependencies..." -ForegroundColor Yellow

# Update core dependencies to latest stable versions
npm install --save express@^5.0.1
npm install --save helmet@^8.0.0
npm install --save uuid@^10.0.0

Write-Host "Updating dev dependencies..." -ForegroundColor Yellow

# Update dev dependencies
npm install --save-dev @types/express@^5.0.0
npm install --save-dev @types/node@^22.10.1
npm install --save-dev typescript@^5.7.2
npm install --save-dev prettier@^3.3.3
npm install --save-dev @vitest/coverage-v8@^3.1.4

Write-Host "Cleaning up..." -ForegroundColor Yellow

# Clean npm cache and reinstall
npm cache clean --force
npm install

Write-Host "Dependencies updated successfully!" -ForegroundColor Green
Write-Host "Run 'npm audit' to check for vulnerabilities" -ForegroundColor Cyan
