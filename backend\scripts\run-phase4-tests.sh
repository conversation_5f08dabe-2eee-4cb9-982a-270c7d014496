#!/bin/bash

# Phase 4: Integration Testing & QC - Comprehensive Test Runner
# This script runs all tests and generates reports for Phase 4 completion

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Create reports directory
mkdir -p reports coverage logs

log "Starting Phase 4: Integration Testing & QC"
log "============================================"

# Check if Docker is available
if command -v docker &> /dev/null; then
    DOCKER_AVAILABLE=true
    log "Docker is available - will run containerized tests"
else
    DOCKER_AVAILABLE=false
    warning "Docker not available - running local tests only"
fi

# Function to run tests with error handling
run_test_suite() {
    local test_name="$1"
    local test_command="$2"
    local report_file="reports/${test_name}-report.json"
    
    log "Running $test_name tests..."
    
    if eval "$test_command" > "logs/${test_name}.log" 2>&1; then
        success "$test_name tests passed"
        return 0
    else
        error "$test_name tests failed - check logs/${test_name}.log"
        return 1
    fi
}

# Initialize test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test 1: Unit Tests
log "Phase 4.1: Running Unit Tests"
log "-----------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "unit" "npm run test:unit"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 2: Integration Tests
log "Phase 4.2: Running Integration Tests"
log "------------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "integration" "npm run test:integration"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 3: End-to-End Tests
log "Phase 4.3: Running End-to-End Tests"
log "-----------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "e2e" "npm run test:e2e"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 4: Performance Tests
log "Phase 4.4: Running Performance Tests"
log "------------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "performance" "npm run performance:test"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 5: Security Tests
log "Phase 4.5: Running Security Tests"
log "---------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "security" "npm run security:test"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 6: Code Coverage
log "Phase 4.6: Generating Code Coverage Report"
log "------------------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "coverage" "npm run test:coverage"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
    
    # Check coverage threshold
    if [ -f "coverage/coverage-summary.json" ]; then
        COVERAGE=$(node -e "
            const coverage = require('./coverage/coverage-summary.json');
            const total = coverage.total;
            console.log(Math.round(total.lines.pct));
        ")
        
        if [ "$COVERAGE" -ge 80 ]; then
            success "Code coverage: ${COVERAGE}% (meets 80% requirement)"
        else
            warning "Code coverage: ${COVERAGE}% (below 80% requirement)"
        fi
    fi
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 7: TypeScript Compilation (Strict Mode)
log "Phase 4.7: Testing TypeScript Strict Mode Compilation"
log "-----------------------------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "typescript" "npm run build"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
    success "TypeScript strict mode compilation successful"
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 8: Linting
log "Phase 4.8: Running Code Quality Checks"
log "--------------------------------------"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "lint" "npm run lint"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 9: Docker Build Test
if [ "$DOCKER_AVAILABLE" = true ]; then
    log "Phase 4.9: Testing Docker Build"
    log "-------------------------------"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test_suite "docker-build" "docker build -t theaigency-backend-test ."; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        success "Docker build successful"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    # Test 10: Docker Compose Test
    log "Phase 4.10: Testing Docker Compose"
    log "----------------------------------"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test_suite "docker-compose" "docker-compose -f docker-compose.test.yml up --abort-on-container-exit"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        success "Docker Compose test successful"
        # Cleanup
        docker-compose -f docker-compose.test.yml down -v
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        # Cleanup on failure
        docker-compose -f docker-compose.test.yml down -v
    fi
fi

# Generate final report
log "Generating Phase 4 Test Report"
log "==============================="

TIMESTAMP=$(date +'%Y-%m-%d %H:%M:%S')
REPORT_FILE="reports/phase4-final-report.json"

cat > "$REPORT_FILE" << EOF
{
  "phase": "Phase 4: Integration Testing & QC",
  "timestamp": "$TIMESTAMP",
  "summary": {
    "totalTests": $TOTAL_TESTS,
    "passedTests": $PASSED_TESTS,
    "failedTests": $FAILED_TESTS,
    "successRate": $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)
  },
  "testSuites": {
    "unit": "$([ -f logs/unit.log ] && echo 'completed' || echo 'not_run')",
    "integration": "$([ -f logs/integration.log ] && echo 'completed' || echo 'not_run')",
    "e2e": "$([ -f logs/e2e.log ] && echo 'completed' || echo 'not_run')",
    "performance": "$([ -f logs/performance.log ] && echo 'completed' || echo 'not_run')",
    "security": "$([ -f logs/security.log ] && echo 'completed' || echo 'not_run')",
    "coverage": "$([ -f logs/coverage.log ] && echo 'completed' || echo 'not_run')",
    "typescript": "$([ -f logs/typescript.log ] && echo 'completed' || echo 'not_run')",
    "lint": "$([ -f logs/lint.log ] && echo 'completed' || echo 'not_run')"
  },
  "dockerTests": {
    "available": $DOCKER_AVAILABLE,
    "build": "$([ -f logs/docker-build.log ] && echo 'completed' || echo 'not_run')",
    "compose": "$([ -f logs/docker-compose.log ] && echo 'completed' || echo 'not_run')"
  },
  "phase4Status": "$([ $FAILED_TESTS -eq 0 ] && echo 'PASSED' || echo 'FAILED')"
}
EOF

# Display final results
log "Phase 4 Test Results Summary"
log "============================"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo "Success Rate: $(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)%"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    success "🎉 Phase 4: Integration Testing & QC COMPLETED SUCCESSFULLY!"
    success "✅ All test suites passed"
    success "✅ Code coverage meets requirements"
    success "✅ TypeScript strict mode enabled"
    success "✅ Docker configuration optimized"
    success "✅ Ready for production deployment"
    echo ""
    success "Phase 4 Status: READY FOR DOCKER LAUNCH"
    exit 0
else
    error "❌ Phase 4: Integration Testing & QC FAILED"
    error "Some test suites failed. Please check the logs in the logs/ directory."
    echo ""
    error "Phase 4 Status: REQUIRES FIXES BEFORE LAUNCH"
    exit 1
fi
