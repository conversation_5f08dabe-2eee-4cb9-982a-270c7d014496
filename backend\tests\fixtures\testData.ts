// Test data fixtures for consistent testing

export const testUsers = {
  basicUser: {
    id: 'user-1',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    paimTier: 'basic',
    roles: ['user'],
  },
  adminUser: {
    id: 'admin-1',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    paimTier: 'enterprise',
    roles: ['admin', 'user'],
  },
  companyAdmin: {
    id: 'company-admin-1',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    paimTier: 'company_admin',
    roles: ['company_admin', 'admin', 'user'],
  },
};

export const testTenants = {
  basicTenant: {
    id: 'tenant-1',
    name: 'Test Company',
    paimTier: 'basic',
    maxSeats: 10,
    usedSeats: 3,
  },
  enterpriseTenant: {
    id: 'tenant-2',
    name: 'Enterprise Corp',
    paimTier: 'enterprise',
    maxSeats: 100,
    usedSeats: 25,
  },
};

export const testPaimInstances = {
  basicPaim: {
    id: 'paim-1',
    tenantId: 'tenant-1',
    name: 'Basic PAIM Instance',
    tier: 'basic',
    status: 'active',
    configuration: {
      maxAgents: 5,
      features: ['basic_ai', 'task_management'],
    },
  },
  enterprisePaim: {
    id: 'paim-2',
    tenantId: 'tenant-2',
    name: 'Enterprise PAIM Instance',
    tier: 'enterprise',
    status: 'active',
    configuration: {
      maxAgents: 50,
      features: ['advanced_ai', 'task_management', 'analytics', 'custom_workflows'],
    },
  },
};

export const testAgents = {
  basicAgent: {
    id: 'agent-1',
    paimInstanceId: 'paim-1',
    name: 'Basic Assistant',
    type: 'assistant',
    status: 'active',
    capabilities: ['text_processing', 'basic_reasoning'],
  },
  advancedAgent: {
    id: 'agent-2',
    paimInstanceId: 'paim-2',
    name: 'Advanced AI Agent',
    type: 'specialist',
    status: 'active',
    capabilities: ['advanced_reasoning', 'multimodal', 'code_generation'],
  },
};

export const testWorkflows = {
  basicWorkflow: {
    id: 'workflow-1',
    tenantId: 'tenant-1',
    name: 'Basic Content Creation',
    description: 'Simple content creation workflow',
    steps: [
      { id: 'step-1', type: 'input', name: 'Gather Requirements' },
      { id: 'step-2', type: 'process', name: 'Generate Content' },
      { id: 'step-3', type: 'output', name: 'Review and Finalize' },
    ],
    status: 'active',
  },
  complexWorkflow: {
    id: 'workflow-2',
    tenantId: 'tenant-2',
    name: 'Multi-Agent Analysis',
    description: 'Complex multi-agent analysis workflow',
    steps: [
      { id: 'step-1', type: 'input', name: 'Data Collection' },
      { id: 'step-2', type: 'process', name: 'Parallel Analysis' },
      { id: 'step-3', type: 'process', name: 'Synthesis' },
      { id: 'step-4', type: 'output', name: 'Report Generation' },
    ],
    status: 'active',
  },
};

export const testPowerOpsData = {
  xpEvents: [
    {
      id: 'xp-1',
      entityId: 'user-1',
      entityType: 'user',
      amount: 100,
      reason: 'Task completion',
      timestamp: new Date('2024-01-01'),
    },
    {
      id: 'xp-2',
      entityId: 'tenant-1',
      entityType: 'organization',
      amount: 500,
      reason: 'Monthly milestone',
      timestamp: new Date('2024-01-01'),
    },
  ],
  badges: [
    {
      id: 'badge-1',
      name: 'First Steps',
      description: 'Complete your first task',
      iconUrl: '/badges/first-steps.png',
      criteria: { tasksCompleted: 1 },
    },
    {
      id: 'badge-2',
      name: 'Power User',
      description: 'Complete 100 tasks',
      iconUrl: '/badges/power-user.png',
      criteria: { tasksCompleted: 100 },
    },
  ],
  achievements: [
    {
      id: 'achievement-1',
      entityId: 'user-1',
      entityType: 'user',
      badgeId: 'badge-1',
      awardedAt: new Date('2024-01-01'),
    },
  ],
};

export const testBillingData = {
  invoices: [
    {
      id: 'invoice-1',
      tenantId: 'tenant-1',
      amount: 99.99,
      currency: 'USD',
      status: 'paid',
      dueDate: new Date('2024-02-01'),
      items: [
        { description: 'Basic PAIM Subscription', amount: 99.99 },
      ],
    },
    {
      id: 'invoice-2',
      tenantId: 'tenant-2',
      amount: 499.99,
      currency: 'USD',
      status: 'pending',
      dueDate: new Date('2024-02-01'),
      items: [
        { description: 'Enterprise PAIM Subscription', amount: 499.99 },
      ],
    },
  ],
  budgets: [
    {
      id: 'budget-1',
      tenantId: 'tenant-1',
      name: 'Monthly AI Usage',
      amount: 1000.00,
      period: 'monthly',
      alertThreshold: 0.8,
    },
  ],
};

export const testApiResponses = {
  healthCheck: {
    status: 'ok',
    timestamp: expect.any(String),
    service: 'theaigency-backend',
  },
  authSuccess: {
    token: expect.any(String),
    user: expect.objectContaining({
      id: expect.any(String),
      email: expect.any(String),
    }),
  },
  errorResponse: {
    error: expect.any(String),
    message: expect.any(String),
  },
};

// Helper functions for test data creation
export function createTestUser(overrides: Partial<typeof testUsers.basicUser> = {}) {
  return { ...testUsers.basicUser, ...overrides };
}

export function createTestPaimInstance(overrides: Partial<typeof testPaimInstances.basicPaim> = {}) {
  return { ...testPaimInstances.basicPaim, ...overrides };
}

export function createTestWorkflow(overrides: Partial<typeof testWorkflows.basicWorkflow> = {}) {
  return { ...testWorkflows.basicWorkflow, ...overrides };
}

export function createTestXpEvent(overrides: Partial<typeof testPowerOpsData.xpEvents[0]> = {}) {
  return { ...testPowerOpsData.xpEvents[0], ...overrides };
}
