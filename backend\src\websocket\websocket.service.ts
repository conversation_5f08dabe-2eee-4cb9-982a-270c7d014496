import { Server as WebSocketServer, WebSocket } from 'ws';
import { Server as HttpServer } from 'http';
import { IncomingMessage } from 'http';
import { authenticateWebSocket, WebSocketWithAuth } from './websocket.middleware';
import { AuthenticatedRequest } from '../types/express-augmentation'; // Import AuthenticatedRequest
import { WebSocketEvent, WebSocketMessage } from './websocket.types'; // Import WebSocketEvent and WebSocketMessage
import logger from '../config/logger';

interface RateLimitEntry {
  count: number;
  lastReset: number;
}

export class WebSocketService {
  private wss: WebSocketServer;
  private clientMessageRateLimits: Map<string, RateLimitEntry> = new Map(); // userId -> RateLimitEntry
  private maxMessagesPerMinute: number = 60; // Max messages per minute per user
  private rateLimitWindowMs: number = 60 * 1000; // 1 minute

  constructor(server: HttpServer) {
    this.wss = new WebSocketServer({
      server,
      verifyClient: (info, done) => {
        // For now, just allow all connections - authentication will be handled in connection handler
        done(true);
      }
    });
    this.initializeWebSocketServer();
  }

  private initializeWebSocketServer(): void {
    this.wss.on('connection', (ws: WebSocket, request: IncomingMessage) => {
      // The request object here is the http.IncomingMessage, which is augmented by authenticateWebSocket
      const authenticatedRequest = request as unknown as AuthenticatedRequest;
      console.log(`Client connected: User ID - ${authenticatedRequest.userId}`);

      ws.on('message', (message: string) => {
        if (!authenticatedRequest.userId) {
          ws.send(JSON.stringify({ event: 'error', payload: 'Unauthorized' }));
          return;
        }

        if (!this.checkRateLimit(authenticatedRequest.userId)) {
          ws.send(JSON.stringify({ event: 'error', payload: 'Rate limit exceeded' }));
          return;
        }

        try {
          const parsedMessage: WebSocketMessage = JSON.parse(message);
          this.handleIncomingMessage(authenticatedRequest.userId, parsedMessage);
        } catch (error) {
          console.error(`Failed to parse WebSocket message from ${authenticatedRequest.userId}:`, error);
          ws.send(JSON.stringify({ event: 'error', payload: 'Invalid message format' }));
        }
      });

      ws.on('close', () => {
        const userId = (authenticatedRequest as AuthenticatedRequest).userId;
        console.log(`Client disconnected: User ID - ${userId}`);
        if (userId) {
          this.clientMessageRateLimits.delete(userId); // Clean up rate limit entry
          // Potentially emit a presence update for this user if they were in a session
          // this.messageHandlers.get(WebSocketEvent.PRESENCE_UPDATE)?.forEach(handler =>
          //   handler(userId, { userId, status: 'offline', workflowId: 'N/A' })
          // );
        }
      });

      ws.on('error', (error) => {
        console.error(`WebSocket error for ${authenticatedRequest.userId}:`, error);
      });
    });

    console.log('WebSocket server initialized');
  }

  private messageHandlers: Map<WebSocketEvent, ((userId: string | undefined, payload: any) => void)[]> = new Map();

  private handleIncomingMessage(userId: string | undefined, message: WebSocketMessage): void {
    const handlers = this.messageHandlers.get(message.event);
    if (handlers) {
      handlers.forEach(handler => handler(userId, message.payload));
    } else {
      console.warn(`No handler registered for WebSocket event: ${message.event}`);
    }
  }

  public on(event: WebSocketEvent, handler: (userId: string | undefined, payload: any) => void): void {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, []);
    }
    this.messageHandlers.get(event)?.push(handler);
  }

  public broadcast(message: string): void {
    this.wss.clients.forEach((client) => {
      if (client.readyState === client.OPEN) {
        client.send(message);
      }
    });
  }

  private checkRateLimit(userId: string): boolean {
    const now = Date.now();
    let entry = this.clientMessageRateLimits.get(userId);

    if (!entry || (now - entry.lastReset > this.rateLimitWindowMs)) {
      entry = { count: 0, lastReset: now };
      this.clientMessageRateLimits.set(userId, entry);
    }

    if (entry.count >= this.maxMessagesPerMinute) {
      logger.warn(`WebSocket rate limit exceeded for user: ${userId}`);
      return false;
    }

    entry.count++;
    return true;
  }

  public sendToUser(userId: string, message: string): void {
    this.wss.clients.forEach((client: WebSocket & { userId?: string }) => {
      if (client.readyState === client.OPEN && client.userId === userId) {
        client.send(message);
      }
    });
  }
}