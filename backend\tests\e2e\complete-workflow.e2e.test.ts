import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { setupTestDatabase, teardownTestDatabase } from '../integration/setup';
import app from '../../src/app';
import { PaimTierEnum } from '../../src/types/db';

describe('Complete Workflow E2E Tests', () => {
  let testApp: any;

  beforeAll(async () => {
    await setupTestDatabase();
    testApp = app;
  });

  afterAll(async () => {
    await teardownTestDatabase();
  });

  describe('User Registration to PAIM Management Workflow', () => {
    let userToken: string;
    let userId: string;
    let paimInstanceId: string;

    it('should complete full user onboarding and PAIM setup workflow', async () => {
      // Step 1: User Registration
      const registrationData = {
        email: '<EMAIL>',
        password: 'WorkflowTest123!',
        firstName: 'Workflow',
        lastName: 'User',
      };

      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send(registrationData)
        .expect(201);

      expect(registerResponse.body).toMatchObject({
        user: expect.objectContaining({
          email: registrationData.email,
          firstName: registrationData.firstName,
          lastName: registrationData.lastName,
        }),
        tokens: expect.objectContaining({
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        }),
      });

      userToken = registerResponse.body.tokens.accessToken;
      userId = registerResponse.body.user.id;

      // Step 2: Verify user can access protected endpoints
      const meResponse = await request(testApp)
        .get('/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(meResponse.body).toMatchObject({
        id: userId,
        email: registrationData.email,
      });

      // Step 3: Create PAIM Instance
      const paimData = {
        name: 'Workflow Test PAIM',
        tier: PaimTierEnum.Basic,
        configuration: {
          maxAgents: 5,
          features: ['basic_ai', 'task_management'],
        },
      };

      const createPaimResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${userToken}`)
        .send(paimData)
        .expect(201);

      expect(createPaimResponse.body).toMatchObject({
        id: expect.any(String),
        name: paimData.name,
        tier: paimData.tier,
        status: 'active',
        configuration: paimData.configuration,
      });

      paimInstanceId = createPaimResponse.body.id;

      // Step 4: Verify PAIM instance can be retrieved
      const getPaimResponse = await request(testApp)
        .get(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(getPaimResponse.body).toMatchObject({
        id: paimInstanceId,
        name: paimData.name,
        tier: paimData.tier,
      });

      // Step 5: Log PowerOps usage
      const usageData = {
        operation: 'ai_generation',
        resourceType: 'text',
        quantity: 1000,
        cost: 0.05,
      };

      const logUsageResponse = await request(testApp)
        .post('/powerops/usage')
        .set('Authorization', `Bearer ${userToken}`)
        .send(usageData)
        .expect(201);

      expect(logUsageResponse.body).toMatchObject({
        id: expect.any(String),
        operation: usageData.operation,
        resourceType: usageData.resourceType,
        quantity: usageData.quantity,
        cost: usageData.cost,
      });

      // Step 6: Award XP for usage
      const xpData = {
        entityId: userId,
        entityType: 'user' as const,
        amount: 100,
        reason: 'First AI generation',
      };

      const awardXpResponse = await request(testApp)
        .post('/gamification/xp/add')
        .set('Authorization', `Bearer ${userToken}`)
        .send(xpData)
        .expect(201);

      expect(awardXpResponse.body).toMatchObject({
        id: expect.any(String),
        entityId: userId,
        entityType: 'user',
        amount: 100,
        reason: 'First AI generation',
      });

      // Step 7: Check user XP
      const getUserXpResponse = await request(testApp)
        .get(`/gamification/xp/user/${userId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(getUserXpResponse.body).toMatchObject({
        userId: userId,
        totalXp: expect.any(Number),
      });

      // Step 8: Request tier change
      const tierChangeRequest = {
        requestedTier: PaimTierEnum.Enterprise,
        justification: 'Need more features for growing usage',
      };

      const tierChangeResponse = await request(testApp)
        .post(`/paim-management/instances/${paimInstanceId}/tier-change-requests`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(tierChangeRequest)
        .expect(202);

      expect(tierChangeResponse.body).toMatchObject({
        id: expect.any(String),
        status: 'pending',
        requestedTier: PaimTierEnum.Enterprise,
        currentTier: PaimTierEnum.Basic,
      });

      // Step 9: Update PAIM configuration
      const updateData = {
        name: 'Updated Workflow PAIM',
        configuration: {
          maxAgents: 10,
          features: ['basic_ai', 'task_management', 'analytics'],
        },
      };

      const updatePaimResponse = await request(testApp)
        .put(`/paim-management/instances/${paimInstanceId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(updatePaimResponse.body).toMatchObject({
        id: paimInstanceId,
        name: updateData.name,
        configuration: updateData.configuration,
      });

      // Step 10: Logout
      const logoutResponse = await request(testApp)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .send({ refreshToken: registerResponse.body.tokens.refreshToken })
        .expect(200);

      expect(logoutResponse.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Logged out'),
      });

      // Step 11: Verify token is invalidated
      await request(testApp)
        .get('/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(401);
    });
  });

  describe('Multi-User Collaboration Workflow', () => {
    let adminToken: string;
    let userToken: string;
    let adminUserId: string;
    let regularUserId: string;
    let sharedPaimId: string;

    it('should handle multi-user collaboration scenario', async () => {
      // Step 1: Create admin user
      const adminRegisterResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'AdminPassword123!',
          firstName: 'Admin',
          lastName: 'User',
        })
        .expect(201);

      adminToken = adminRegisterResponse.body.tokens.accessToken;
      adminUserId = adminRegisterResponse.body.user.id;

      // Step 2: Create regular user
      const userRegisterResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'UserPassword123!',
          firstName: 'Regular',
          lastName: 'User',
        })
        .expect(201);

      userToken = userRegisterResponse.body.tokens.accessToken;
      regularUserId = userRegisterResponse.body.user.id;

      // Step 3: Admin creates shared PAIM instance
      const sharedPaimResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Shared Collaboration PAIM',
          tier: PaimTierEnum.Enterprise,
          configuration: {
            maxAgents: 20,
            features: ['advanced_ai', 'collaboration', 'analytics'],
          },
        })
        .expect(201);

      sharedPaimId = sharedPaimResponse.body.id;

      // Step 4: Both users log PowerOps usage
      const adminUsageResponse = await request(testApp)
        .post('/powerops/usage')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          operation: 'ai_generation',
          resourceType: 'text',
          quantity: 2000,
          cost: 0.10,
        })
        .expect(201);

      const userUsageResponse = await request(testApp)
        .post('/powerops/usage')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          operation: 'image_processing',
          resourceType: 'image',
          quantity: 5,
          cost: 0.25,
        })
        .expect(201);

      expect(adminUsageResponse.body.operation).toBe('ai_generation');
      expect(userUsageResponse.body.operation).toBe('image_processing');

      // Step 5: Award XP to both users
      await request(testApp)
        .post('/gamification/xp/add')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          entityId: adminUserId,
          entityType: 'user',
          amount: 200,
          reason: 'Admin task completion',
        })
        .expect(201);

      await request(testApp)
        .post('/gamification/xp/add')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          entityId: regularUserId,
          entityType: 'user',
          amount: 150,
          reason: 'User task completion',
        })
        .expect(201);

      // Step 6: Check leaderboard
      const leaderboardResponse = await request(testApp)
        .get('/gamification/leaderboard')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({ entityType: 'user', limit: '10' })
        .expect(200);

      expect(Array.isArray(leaderboardResponse.body)).toBe(true);
      expect(leaderboardResponse.body.length).toBeGreaterThan(0);

      // Step 7: Admin updates shared PAIM
      const updateResponse = await request(testApp)
        .put(`/paim-management/instances/${sharedPaimId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Updated Shared PAIM',
          configuration: {
            maxAgents: 25,
            features: ['advanced_ai', 'collaboration', 'analytics', 'reporting'],
          },
        })
        .expect(200);

      expect(updateResponse.body.name).toBe('Updated Shared PAIM');

      // Step 8: Regular user can view but not modify shared PAIM
      const viewPaimResponse = await request(testApp)
        .get(`/paim-management/instances/${sharedPaimId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(viewPaimResponse.body.id).toBe(sharedPaimId);

      // Step 9: Regular user cannot delete shared PAIM (should fail)
      await request(testApp)
        .delete(`/paim-management/instances/${sharedPaimId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      // Step 10: Admin can delete shared PAIM
      const deleteResponse = await request(testApp)
        .delete(`/paim-management/instances/${sharedPaimId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(deleteResponse.body.success).toBe(true);

      // Step 11: Verify PAIM is deleted
      await request(testApp)
        .get(`/paim-management/instances/${sharedPaimId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('Error Recovery Workflow', () => {
    let userToken: string;

    beforeAll(async () => {
      const registerResponse = await request(testApp)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'ErrorTest123!',
          firstName: 'Error',
          lastName: 'Test',
        });

      userToken = registerResponse.body.tokens.accessToken;
    });

    it('should handle and recover from various error scenarios', async () => {
      // Test 1: Handle invalid PAIM creation gracefully
      const invalidPaimResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: '', // Invalid: empty name
          tier: 'invalid_tier', // Invalid tier
        })
        .expect(400);

      expect(invalidPaimResponse.body.error).toBeDefined();

      // Test 2: Create valid PAIM after error
      const validPaimResponse = await request(testApp)
        .post('/paim-management/instances')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'Recovery Test PAIM',
          tier: PaimTierEnum.Basic,
          configuration: { maxAgents: 5 },
        })
        .expect(201);

      const paimId = validPaimResponse.body.id;

      // Test 3: Handle invalid usage logging
      await request(testApp)
        .post('/powerops/usage')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          operation: '', // Invalid: empty operation
          quantity: -1, // Invalid: negative quantity
        })
        .expect(400);

      // Test 4: Log valid usage after error
      const validUsageResponse = await request(testApp)
        .post('/powerops/usage')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          operation: 'ai_generation',
          resourceType: 'text',
          quantity: 1000,
          cost: 0.05,
        })
        .expect(201);

      expect(validUsageResponse.body.operation).toBe('ai_generation');

      // Test 5: Handle non-existent resource access
      await request(testApp)
        .get('/paim-management/instances/nonexistent-id')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      // Test 6: Successfully access existing resource
      const existingPaimResponse = await request(testApp)
        .get(`/paim-management/instances/${paimId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(existingPaimResponse.body.id).toBe(paimId);
    });
  });
});
