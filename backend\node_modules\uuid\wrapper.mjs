import uuid from './dist/index.js';
export const v1 = uuid.v1;
export const v1ToV6 = uuid.v1ToV6;
export const v3 = uuid.v3;
export const v4 = uuid.v4;
export const v5 = uuid.v5;
export const v6 = uuid.v6;
export const v6ToV1 = uuid.v6ToV1;
export const v7 = uuid.v7;
export const NIL = uuid.NIL;
export const MAX = uuid.MAX;
export const version = uuid.version;
export const validate = uuid.validate;
export const stringify = uuid.stringify;
export const parse = uuid.parse;
