# Test-specific Dockerfile for running comprehensive tests
FROM node:20-alpine

# Set the working directory
WORKDIR /app/backend

# Install system dependencies for testing
RUN apk add --no-cache \
    curl \
    postgresql-client \
    redis \
    git

# Copy package files
COPY package.json package-lock.json ./

# Install all dependencies including dev dependencies for testing
RUN npm ci --include=dev

# Copy source code and configuration files
COPY . .

# Create test directories
RUN mkdir -p coverage reports logs

# Set proper permissions
RUN chown -R node:node /app/backend
USER node

# Expose ports for testing
EXPOSE 3000 3001 3002

# Health check for test container
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Default command runs all tests
CMD ["npm", "run", "test:all"]
