{
  "compilerOptions": {
    "target": "ES2022",
    "module": "CommonJS",
    "lib": ["ES2022"],
    "types": ["node", "jest"],
    "outDir": "./dist",
    "rootDir": "./",
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true, // Re-enabled for Phase 4 production readiness
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true, // Re-enabled for Phase 4 production readiness
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "baseUrl": "./",
    "paths": {
      "@config/*": ["src/config/*"],
      "@database/*": ["src/database/*"],
      "@utils/*": ["src/utils/*"],
      "@types/*": ["src/types/*"]
    },
    "typeRoots": ["./node_modules/@types", "./src/types"]
  },
  "include": [
    "src/**/*.ts",
    "migrations/**/*.ts",
    "knexfile.ts",
    "src/types/express-augmentation.d.ts"
  ],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}