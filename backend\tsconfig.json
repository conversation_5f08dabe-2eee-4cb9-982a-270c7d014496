{
  "compilerOptions": {
    "target": "ES2022",
    "module": "CommonJS",
    "lib": ["ES2022"],
    "types": ["node", "jest"],
    "outDir": "./dist",
    "rootDir": "./",
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": false, // Temporarily disabled for Phase 4 build compatibility
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false, // Temporarily disabled for Phase 4 build compatibility
    "exactOptionalPropertyTypes": false, // Temporarily disabled for Phase 4 build compatibility
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "baseUrl": "./",
    "paths": {
      "@config/*": ["src/config/*"],
      "@database/*": ["src/database/*"],
      "@utils/*": ["src/utils/*"],
      "@types/*": ["src/types/*"]
    },
    "typeRoots": ["./node_modules/@types", "./src/types"]
  },
  "include": [
    "src/**/*.ts",
    "migrations/**/*.ts",
    "knexfile.ts",
    "src/types/express-augmentation.d.ts"
  ],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}