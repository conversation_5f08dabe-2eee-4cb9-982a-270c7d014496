"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const uuid_1 = require("uuid"); // Import uuidv4
const logger_1 = __importDefault(require("./config/logger")); // Import logger
const authentication_1 = require("./middleware/authentication"); // Import authentication middleware
const auth_controller_1 = __importDefault(require("./auth/auth.controller"));
const paim_controller_1 = __importDefault(require("./paim/paim.controller")); // Import PAIM controller
const agent_controller_1 = require("./agent/agent.controller"); // Import AgentController
const agent_middleware_1 = require("./agent/agent.middleware"); // Import agentMiddleware
const aigency_controller_1 = __importDefault(require("./aigency/aigency.controller")); // Import AIgency routes
const audit_controller_1 = require("./audit/audit.controller"); // Import AuditController
const audit_middleware_1 = require("./audit/audit.middleware"); // Import auditMiddleware
const cultural_sensitivity_controller_1 = require("./cultural-sensitivity/cultural-sensitivity.controller"); // Import CulturalSensitivityController
const db_1 = __importDefault(require("./database/db")); // Import the knex database instance
const dotenv_1 = __importDefault(require("dotenv"));
const powerops_controller_1 = require("./powerops/powerops.controller"); // Import PowerOpsController
const powerops_middleware_1 = require("./powerops/powerops.middleware"); // Import PowerOpsMiddleware
const workflow_collaboration_controller_1 = require("./workflow-collaboration/workflow-collaboration.controller");
const workflow_collaboration_service_1 = require("./workflow-collaboration/workflow-collaboration.service");
const workflow_collaboration_repository_1 = require("./workflow-collaboration/workflow-collaboration.repository");
const workflow_collaboration_middleware_1 = require("./workflow-collaboration/workflow-collaboration.middleware");
const organization_controller_1 = require("./organization/organization.controller"); // Import OrganizationController
const organization_service_1 = require("./organization/organization.service"); // Import OrganizationService
const organization_repository_1 = require("./organization/organization.repository"); // Import OrganizationRepository
const email_service_1 = require("./utils/email.service"); // Import EmailService
const billing_controller_1 = require("./billing/billing.controller"); // Import BillingController
const billing_service_1 = require("./billing/billing.service"); // Import BillingService
const billing_repository_1 = require("./billing/billing.repository"); // Import BillingRepository
const agent_framework_1 = __importDefault(require("./agent-framework")); // Import Agent Framework
const notification_service_1 = require("./notifications/notification.service");
// Load environment-specific config first
const envFile = process.env.NODE_ENV === 'production'
    ? '.env.production'
    : '.env.development';
dotenv_1.default.config({ path: envFile });
// Fallback to .env
dotenv_1.default.config();
const app = (0, express_1.default)();
// Initialize Agent Framework
const agentFramework = agent_framework_1.default.getInstance();
// Initialize framework with default configuration
agentFramework.initialize().catch(error => {
    logger_1.default.error('Failed to initialize Agent Framework:', error);
});
// Initialize NotificationService (needed by other services)
const notificationService = new notification_service_1.NotificationService({}, {}); // Placeholder for now
// Initialize PowerOpsController and Middleware
const powerOpsController = new powerops_controller_1.PowerOpsController(notificationService);
const powerOpsMiddleware = new powerops_middleware_1.PowerOpsMiddleware();
// Initialize Workflow Collaboration components
const workflowCollaborationRepository = new workflow_collaboration_repository_1.WorkflowCollaborationRepository();
// Create placeholder CollaborationEvents for now
const collaborationEvents = {};
// Create placeholder CollaborationSessionService for now
const collaborationSessionService = {};
const workflowCollaborationService = new workflow_collaboration_service_1.WorkflowCollaborationService(workflowCollaborationRepository, collaborationEvents, collaborationSessionService);
const workflowCollaborationController = new workflow_collaboration_controller_1.WorkflowCollaborationController(workflowCollaborationService);
const workflowCollaborationMiddleware = new workflow_collaboration_middleware_1.WorkflowCollaborationMiddleware();
// Initialize PAIM components
const paimController = new paim_controller_1.default(notificationService, {}); // Placeholder for WebSocketService
// Initialize Organization components
const organizationRepository = new organization_repository_1.OrganizationRepository(db_1.default);
const emailService = new email_service_1.EmailService(); // Instantiate EmailService
const organizationService = new organization_service_1.OrganizationService(organizationRepository, emailService);
const organizationController = new organization_controller_1.OrganizationController(organizationService);
// Initialize Billing components
const billingRepository = new billing_repository_1.BillingRepository(db_1.default);
const billingService = new billing_service_1.BillingService(billingRepository);
const billingController = new billing_controller_1.BillingController(billingService);
// Middleware
app.use(express_1.default.json()); // Body parser for JSON requests
app.use((0, cors_1.default)()); // Enable CORS for all origins (adjust as needed for production)
app.use((0, helmet_1.default)()); // Add security headers
app.use(audit_middleware_1.auditMiddleware); // Add global audit middleware
app.use(powerOpsMiddleware.logApiUsage); // Add PowerOps API usage logging middleware
// Middleware to add correlation ID to requests and logs
app.use((req, res, next) => {
    const correlationId = req.headers['x-correlation-id'] || (0, uuid_1.v4)();
    req.correlationId = correlationId; // Attach to request object
    logger_1.default.defaultMeta = { correlationId }; // Add to default logger metadata
    next();
});
// Rate limiting to prevent brute-force attacks
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again after 15 minutes',
});
app.use(apiLimiter);
// Apply authentication middleware to all routes that require it
// For now, applying globally. In a real app, you might apply it to specific routes or groups of routes.
app.use(authentication_1.authenticate);
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'theaigency-backend'
    });
});
// Routes
app.use('/auth', auth_controller_1.default);
app.use('/paim-management', paimController.router); // Add PAIM routes
app.use('/aigency', aigency_controller_1.default); // Add AIgency routes
// Workflow Collaboration Routes
app.get('/workflows', workflowCollaborationController.getAllWorkflows);
app.post('/workflows', workflowCollaborationMiddleware.validateCreateWorkflow, workflowCollaborationController.createWorkflow);
app.get('/workflows/:workflowId', workflowCollaborationController.getWorkflowById);
app.put('/workflows/:workflowId', workflowCollaborationController.updateWorkflow);
app.delete('/workflows/:workflowId', workflowCollaborationController.deleteWorkflow);
app.get('/tasks', workflowCollaborationController.getAllTasks);
app.post('/tasks', workflowCollaborationMiddleware.validateCreateTask, workflowCollaborationController.createTask);
app.get('/tasks/:taskId', workflowCollaborationController.getTaskById);
app.put('/tasks/:taskId', workflowCollaborationController.updateTask);
app.delete('/tasks/:taskId', workflowCollaborationController.deleteTask);
app.post('/collaboration/sessions', workflowCollaborationMiddleware.validateStartCollaborationSession, workflowCollaborationController.startCollaborationSession);
app.post('/collaboration/sessions/:sessionId/join', workflowCollaborationController.joinCollaborationSession);
app.post('/collaboration/sessions/:sessionId/leave', workflowCollaborationController.leaveCollaborationSession);
app.post('/cross-tenant-communication/messages', workflowCollaborationMiddleware.validateCrossTenantMessage, workflowCollaborationController.sendCrossTenantMessage);
app.get('/notifications', workflowCollaborationController.getAllNotifications);
app.post('/notifications/:notificationId/read', workflowCollaborationController.markNotificationAsRead);
app.post('/workflows/:workflowId/share', workflowCollaborationMiddleware.validateShareWorkflow, workflowCollaborationController.shareWorkflow);
app.delete('/workflows/:workflowId/share/:permissionId', workflowCollaborationController.deleteWorkflowShare);
app.post('/tasks/:taskId/delegate', workflowCollaborationMiddleware.validateDelegateTask, workflowCollaborationController.delegateTask);
app.get('/workspaces', workflowCollaborationController.getAllWorkspaces);
app.post('/workspaces', workflowCollaborationMiddleware.validateCreateWorkspace, workflowCollaborationController.createWorkspace);
app.get('/workspaces/:workspaceId', workflowCollaborationController.getWorkspaceById);
app.put('/workspaces/:workspaceId', workflowCollaborationController.updateWorkspace);
app.delete('/workspaces/:workspaceId', workflowCollaborationController.deleteWorkspace);
app.get('/teams', workflowCollaborationController.getAllTeams);
app.post('/teams', workflowCollaborationMiddleware.validateCreateTeam, workflowCollaborationController.createTeam);
app.get('/teams/:teamId', workflowCollaborationController.getTeamById);
app.put('/teams/:teamId', workflowCollaborationController.updateTeam);
app.delete('/teams/:teamId', workflowCollaborationController.deleteTeam);
// Add PowerOps routes
app.post('/powerops/usage', powerOpsController.logPowerOpsUsage);
app.get('/powerops/usage', powerOpsController.getPowerOpsUsage);
app.get('/gamification/xp/user/:id', powerOpsController.getXpByUser);
app.get('/gamification/xp/org/:id', powerOpsController.getXpByOrg);
app.post('/gamification/xp/add', powerOpsController.awardXp);
app.get('/gamification/badges', powerOpsController.getAllBadges);
app.get('/gamification/badges/user/:id', powerOpsController.getBadgesByUser);
app.post('/gamification/badges/award', powerOpsController.awardBadge);
app.get('/gamification/achievements', powerOpsController.getAchievements);
app.post('/gamification/achievements', powerOpsController.grantAchievement);
app.get('/gamification/streaks', powerOpsController.getStreaks);
app.get('/cost-management/budgets', powerOpsController.getBudgets);
app.post('/cost-management/budgets', powerOpsController.createBudget);
app.put('/cost-management/budgets/:budgetId', powerOpsController.updateBudget);
app.delete('/cost-management/budgets/:budgetId', powerOpsController.deleteBudget);
app.get('/billing/invoices', powerOpsController.getInvoices);
app.post('/billing/invoices', powerOpsController.createInvoice);
app.post('/billing/payments', powerOpsController.processPayment);
app.get('/gamification/leaderboard', powerOpsController.getLeaderboard);
app.get('/cost-management/recommendations', powerOpsController.getCostOptimizationRecommendations);
app.get('/resource-management/limits', powerOpsController.getResourceUsageLimits);
app.post('/resource-management/limits', powerOpsController.setResourceUsageLimit);
app.get('/notifications', powerOpsController.getNotifications);
app.post('/notifications', powerOpsController.createNotification);
// Initialize AgentController and use its router
const agentController = new agent_controller_1.AgentController();
app.use('/agents', agent_middleware_1.agentMiddleware, agentController.router); // Add Agent routes with middleware
// Initialize AuditController and use its router
const auditController = new audit_controller_1.AuditController();
app.use('/audit', auditController.router); // Add Audit routes
// Initialize CulturalSensitivityController and use its router
const culturalSensitivityController = new cultural_sensitivity_controller_1.CulturalSensitivityController(db_1.default);
app.use('/cultural-sensitivity', culturalSensitivityController.router); // Add Cultural Sensitivity routes
// Add Billing routes
app.use('/api/v1/billing', billingController.router);
// Add Organization routes
app.use('/api/v1/orgs', organizationController.router);
// Global Error Handler
app.use((err, req, res, next) => {
    console.error('Global Error Handler:', err); // Log the error for debugging
    const statusCode = err.statusCode || 500;
    const message = err.message || 'Internal Server Error';
    // In production, avoid sending sensitive error details to the client
    const errorResponse = {
        timestamp: new Date().toISOString(),
        status: statusCode,
        error: err.name || 'ServerError',
        message: process.env.NODE_ENV === 'production' ? 'Internal Server Error' : message,
        path: req.originalUrl,
        details: err.details || undefined, // Include validation details if available
    };
    res.status(statusCode).json(errorResponse);
});
exports.default = app;
