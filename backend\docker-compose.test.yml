version: '3.8'

services:
  # Test Database
  test-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: theaigency_test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - test_db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d theaigency_test_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Redis
  test-redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Qdrant
  test-qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6336:6333" # REST API
      - "6337:6334" # gRPC
    volumes:
      - test_qdrant_data:/qdrant/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Test Runner
  backend-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      NODE_ENV: test
      DATABASE_URL: ***********************************************/theaigency_test_db
      REDIS_URL: redis://test-redis:6379
      QDRANT_URL: http://test-qdrant:6333
      JWT_SECRET: test_jwt_secret_key_for_testing_only
      # Test-specific configurations
      TEST_TIMEOUT: 30000
      TEST_PARALLEL: "false"
      LOG_LEVEL: error
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      test-qdrant:
        condition: service_healthy
    volumes:
      - .:/app/backend
      - /app/backend/node_modules
      - test_coverage:/app/backend/coverage
    command: ["npm", "run", "test:all"]
    networks:
      - test-network

  # Integration Test Runner
  integration-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      NODE_ENV: test
      DATABASE_URL: ***********************************************/theaigency_test_db
      REDIS_URL: redis://test-redis:6379
      QDRANT_URL: http://test-qdrant:6333
      JWT_SECRET: test_jwt_secret_key_for_testing_only
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      test-qdrant:
        condition: service_healthy
    volumes:
      - .:/app/backend
      - /app/backend/node_modules
    command: ["npm", "run", "test:integration"]
    networks:
      - test-network

  # Performance Test Runner
  performance-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      NODE_ENV: test
      DATABASE_URL: ***********************************************/theaigency_test_db
      REDIS_URL: redis://test-redis:6379
      QDRANT_URL: http://test-qdrant:6333
      JWT_SECRET: test_jwt_secret_key_for_testing_only
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      test-qdrant:
        condition: service_healthy
    volumes:
      - .:/app/backend
      - /app/backend/node_modules
    command: ["npm", "run", "performance:test"]
    networks:
      - test-network

  # Security Test Runner
  security-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      NODE_ENV: test
      DATABASE_URL: ***********************************************/theaigency_test_db
      REDIS_URL: redis://test-redis:6379
      QDRANT_URL: http://test-qdrant:6333
      JWT_SECRET: test_jwt_secret_key_for_testing_only
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      test-qdrant:
        condition: service_healthy
    volumes:
      - .:/app/backend
      - /app/backend/node_modules
    command: ["npm", "run", "security:test"]
    networks:
      - test-network

volumes:
  test_db_data:
  test_qdrant_data:
  test_coverage:

networks:
  test-network:
    driver: bridge
