# TheAIgency Backend Environment Configuration
# Copy this file to .env and configure with your actual values

# Application Configuration
NODE_ENV=production
PORT=3000

# Database Configuration
DATABASE_URL=postgres://postgres:postgres@localhost:5432/theaigency_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Qdrant Vector Database
QDRANT_URL=http://localhost:6333

# JWT Secret (CHANGE THIS IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters

# Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your-project-id-here
APPWRITE_DATABASE_ID=your-database-id-here
APPWRITE_API_KEY=your-api-key-here

# External AI API Keys (Optional)
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
RUNWAYML_API_KEY=your-runwayml-api-key
ADOBE_FIREFLY_API_KEY=your-adobe-firefly-api-key

# Logging Configuration
LOG_LEVEL=info